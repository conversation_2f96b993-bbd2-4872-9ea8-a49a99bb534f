import { useEffect, useCallback, useRef } from 'react';

interface PerformanceMetrics {
  lcp?: number; // Largest Contentful Paint
  fid?: number; // First Input Delay
  cls?: number; // Cumulative Layout Shift
  fcp?: number; // First Contentful Paint
  ttfb?: number; // Time to First Byte
  loadTime?: number;
  domContentLoaded?: number;
  memoryUsage?: number;
  connectionType?: string;
  deviceType?: 'mobile' | 'tablet' | 'desktop';
}

interface PerformanceConfig {
  enableLogging?: boolean;
  enableReporting?: boolean;
  reportingEndpoint?: string;
  sampleRate?: number;
}

export const usePerformanceMonitor = (config: PerformanceConfig = {}) => {
  const {
    enableLogging = process.env.NODE_ENV === 'development',
    enableReporting = false,
    reportingEndpoint,
    sampleRate = 1.0
  } = config;

  const metricsRef = useRef<PerformanceMetrics>({});
  const observersRef = useRef<PerformanceObserver[]>([]);

  // Detect device type
  const getDeviceType = useCallback((): 'mobile' | 'tablet' | 'desktop' => {
    const width = window.innerWidth;
    if (width < 768) return 'mobile';
    if (width < 1024) return 'tablet';
    return 'desktop';
  }, []);

  // Get connection information
  const getConnectionInfo = useCallback(() => {
    const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection;
    return connection ? connection.effectiveType || 'unknown' : 'unknown';
  }, []);

  // Log metrics
  const logMetrics = useCallback((metric: string, value: number, unit: string = 'ms') => {
    if (enableLogging) {
      console.log(`🚀 Performance: ${metric} = ${value.toFixed(2)}${unit}`);
    }
  }, [enableLogging]);

  // Report metrics to endpoint
  const reportMetrics = useCallback(async (metrics: PerformanceMetrics) => {
    if (!enableReporting || !reportingEndpoint || Math.random() > sampleRate) return;

    try {
      await fetch(reportingEndpoint, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          ...metrics,
          timestamp: Date.now(),
          url: window.location.href,
          userAgent: navigator.userAgent
        })
      });
    } catch (error) {
      console.warn('Failed to report performance metrics:', error);
    }
  }, [enableReporting, reportingEndpoint, sampleRate]);

  // Measure Core Web Vitals
  const measureCoreWebVitals = useCallback(() => {
    // Largest Contentful Paint (LCP)
    if ('PerformanceObserver' in window) {
      try {
        const lcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          const lastEntry = entries[entries.length - 1] as any;
          if (lastEntry) {
            const lcp = lastEntry.startTime;
            metricsRef.current.lcp = lcp;
            logMetrics('LCP', lcp);
          }
        });
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        observersRef.current.push(lcpObserver);
      } catch (e) {
        console.warn('LCP measurement not supported');
      }

      // First Input Delay (FID)
      try {
        const fidObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            const fid = entry.processingStart - entry.startTime;
            metricsRef.current.fid = fid;
            logMetrics('FID', fid);
          });
        });
        fidObserver.observe({ entryTypes: ['first-input'] });
        observersRef.current.push(fidObserver);
      } catch (e) {
        console.warn('FID measurement not supported');
      }

      // Cumulative Layout Shift (CLS)
      try {
        let clsValue = 0;
        const clsObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            if (!entry.hadRecentInput) {
              clsValue += entry.value;
            }
          });
          metricsRef.current.cls = clsValue;
          logMetrics('CLS', clsValue, '');
        });
        clsObserver.observe({ entryTypes: ['layout-shift'] });
        observersRef.current.push(clsObserver);
      } catch (e) {
        console.warn('CLS measurement not supported');
      }

      // First Contentful Paint (FCP)
      try {
        const fcpObserver = new PerformanceObserver((list) => {
          const entries = list.getEntries();
          entries.forEach((entry: any) => {
            if (entry.name === 'first-contentful-paint') {
              const fcp = entry.startTime;
              metricsRef.current.fcp = fcp;
              logMetrics('FCP', fcp);
            }
          });
        });
        fcpObserver.observe({ entryTypes: ['paint'] });
        observersRef.current.push(fcpObserver);
      } catch (e) {
        console.warn('FCP measurement not supported');
      }
    }
  }, [logMetrics]);

  // Measure navigation timing
  const measureNavigationTiming = useCallback(() => {
    if ('performance' in window && 'timing' in performance) {
      const timing = performance.timing;
      const navigation = performance.navigation;

      // Time to First Byte
      const ttfb = timing.responseStart - timing.navigationStart;
      metricsRef.current.ttfb = ttfb;
      logMetrics('TTFB', ttfb);

      // DOM Content Loaded
      const domContentLoaded = timing.domContentLoadedEventEnd - timing.navigationStart;
      metricsRef.current.domContentLoaded = domContentLoaded;
      logMetrics('DOM Content Loaded', domContentLoaded);

      // Load Time
      const loadTime = timing.loadEventEnd - timing.navigationStart;
      metricsRef.current.loadTime = loadTime;
      logMetrics('Load Time', loadTime);
    }
  }, [logMetrics]);

  // Measure memory usage
  const measureMemoryUsage = useCallback(() => {
    if ('memory' in performance) {
      const memory = (performance as any).memory;
      const memoryUsage = memory.usedJSHeapSize / 1024 / 1024; // MB
      metricsRef.current.memoryUsage = memoryUsage;
      logMetrics('Memory Usage', memoryUsage, 'MB');
    }
  }, [logMetrics]);

  // Get all metrics
  const getMetrics = useCallback((): PerformanceMetrics => {
    return {
      ...metricsRef.current,
      connectionType: getConnectionInfo(),
      deviceType: getDeviceType()
    };
  }, [getConnectionInfo, getDeviceType]);

  // Initialize monitoring
  useEffect(() => {
    // Wait for page load
    const initializeMonitoring = () => {
      measureNavigationTiming();
      measureCoreWebVitals();
      measureMemoryUsage();

      // Report metrics after a delay
      setTimeout(() => {
        const metrics = getMetrics();
        reportMetrics(metrics);
      }, 5000);
    };

    if (document.readyState === 'complete') {
      initializeMonitoring();
    } else {
      window.addEventListener('load', initializeMonitoring);
    }

    // Cleanup observers on unmount
    return () => {
      observersRef.current.forEach(observer => observer.disconnect());
      observersRef.current = [];
    };
  }, [measureNavigationTiming, measureCoreWebVitals, measureMemoryUsage, getMetrics, reportMetrics]);

  return {
    getMetrics,
    logMetrics,
    reportMetrics: () => reportMetrics(getMetrics())
  };
};

// Hook for measuring component render performance
export const useRenderPerformance = (componentName: string) => {
  const renderStartRef = useRef<number>(0);

  useEffect(() => {
    renderStartRef.current = performance.now();
  });

  useEffect(() => {
    const renderTime = performance.now() - renderStartRef.current;
    if (process.env.NODE_ENV === 'development') {
      console.log(`🎨 Render: ${componentName} = ${renderTime.toFixed(2)}ms`);
    }
  });

  return {
    markRenderStart: () => {
      renderStartRef.current = performance.now();
    },
    getRenderTime: () => {
      return performance.now() - renderStartRef.current;
    }
  };
};

// Hook for measuring API call performance
export const useApiPerformance = () => {
  const measureApiCall = useCallback(async <T>(
    apiCall: () => Promise<T>,
    apiName: string
  ): Promise<T> => {
    const startTime = performance.now();
    try {
      const result = await apiCall();
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`🌐 API: ${apiName} = ${duration.toFixed(2)}ms`);
      }
      
      return result;
    } catch (error) {
      const endTime = performance.now();
      const duration = endTime - startTime;
      
      if (process.env.NODE_ENV === 'development') {
        console.log(`❌ API Error: ${apiName} = ${duration.toFixed(2)}ms`);
      }
      
      throw error;
    }
  }, []);

  return { measureApiCall };
};
