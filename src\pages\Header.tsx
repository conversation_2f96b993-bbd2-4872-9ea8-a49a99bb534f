import React, { useState, useEffect } from "react";
import {
  Menu,
  X,
  Home,
  Briefcase,
  Settings,
  Search,
  Moon,
  Sun,
  LogOut,
  User,
  Shield,
  ExternalLink,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { But<PERSON> } from "@/components/ui/button";
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar";
import { Input } from "@/components/ui/input";
import { Link, useLocation, useNavigate } from "react-router-dom";
import { useTheme } from "@/components/ui/theme-provider";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useAuth } from "@/contexts/AuthContext";
import { searchUserByName, getUsers } from "@/services/firebase-db";
import type { UserProfile } from "@/services/firebase-db";

// Add the animation keyframes at the top of the file
const gradientAnimation = `
@keyframes gradientAnimation {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
`;

export function ModeToggle() {
  const { setTheme } = useTheme()

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="icon">
          <Sun className="h-[1.2rem] w-[1.2rem] scale-100 rotate-0 transition-all dark:scale-0 dark:-rotate-90" />
          <Moon className="absolute h-[1.2rem] w-[1.2rem] scale-0 rotate-90 transition-all dark:scale-100 dark:rotate-0" />
          <span className="sr-only">Toggle theme</span>
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuItem onClick={() => setTheme("light")}>
          Light
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme("dark")}>
          Dark
        </DropdownMenuItem>
        <DropdownMenuItem onClick={() => setTheme("system")}>
          System
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  )
}

export default function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [searchResults, setSearchResults] = useState<UserProfile[]>([]);
  const [allUsers, setAllUsers] = useState<UserProfile[]>([]);
  const [isLoadingUsers, setIsLoadingUsers] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const { user, userProfile, signInWithGoogle, signOut, loading } = useAuth();

  // Fetch all users once when component mounts
  useEffect(() => {
    const fetchAllUsers = async () => {
      setIsLoadingUsers(true);
      try {
        const { users } = await getUsers(1000); // Fetch up to 1000 users
        setAllUsers(users);
      } catch (error) {
        console.error('Error fetching users:', error);
      } finally {
        setIsLoadingUsers(false);
      }
    };

    fetchAllUsers();
  }, []);

  // Auto-redirect to create-profile if user is logged in but has no profile
  useEffect(() => {
    if (!loading && user && !user.hasProfile && !userProfile) {
      // Only redirect if not already on create-profile page
      if (location.pathname !== '/create-profile') {
        navigate('/create-profile');
      }
    }
  }, [user, userProfile, loading, location.pathname, navigate]);

  // Search functionality (client-side)
  const handleSearch = async (query: string) => {
    if (!query.trim()) {
      setSearchResults([]);
      return;
    }

    try {
      const results = await searchUserByName(query, allUsers);
      setSearchResults(results);
    } catch (error) {
      console.error('Error searching users:', error);
      setSearchResults([]);
    }
  };

  // Debounced search
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      handleSearch(searchQuery);
    }, 100); // Reduced delay since it's client-side now

    return () => clearTimeout(timeoutId);
  }, [searchQuery, allUsers]);

  const handleSearchResultClick = (username: string) => {
    setIsSearchOpen(false);
    setSearchQuery("");
    setSearchResults([]);
    navigate(`/${username}`);
  };

  const isActive = (path: string) => {
    if (path === "/") {
      return location.pathname === "/";
    }
    if (path === "/Profile") {
      // Check if current path matches user's profile URL
      return userProfile?.username ? location.pathname === `/${userProfile.username}` : false;
    }
    return location.pathname.startsWith(path);
  };

  // Get link classes based on active state
  const getLinkClasses = (path: string, isMobile: boolean = false) => {
    return cn(
      "transition-all duration-300 relative py-2 px-4 rounded-lg",
      isActive(path)
        ? "text-primary font-medium bg-primary/10"
        : "text-foreground hover:text-primary hover:bg-primary/5",
      isMobile && "border-b border-border w-full",
      isActive(path) && isMobile && "border-primary"
    );
  };

  const handleSignIn = async () => {
    try {
      await signInWithGoogle();
      // The useEffect hook will handle the automatic routing based on user profile status
    } catch (error) {
      console.error('Error signing in:', error);
    }
  };

  const handleSignOut = async () => {
    try {
      await signOut();
    } catch (error) {
      console.error('Error signing out:', error);
    }
  };

  // Get user initials for avatar fallback
  const getUserInitials = () => {
    if (userProfile?.displayName) {
      return userProfile.displayName.split(' ').map(name => name[0]).join('').toUpperCase().slice(0, 2);
    }
    if (user?.displayName) {
      return user.displayName.split(' ').map(name => name[0]).join('').toUpperCase().slice(0, 2);
    }
    if (user?.email) {
      return user.email[0].toUpperCase();
    }
    return 'U';
  };

  // Get the best photo URL (prioritize Firestore, fallback to Google auth)
  const getPhotoURL = () => {
    const firestorePhoto = userProfile?.photoURL;
    const googlePhoto = user?.photoURL;
    
    // Return the first valid photo URL
    return firestorePhoto || googlePhoto || undefined;
  };

  // Get the best display name (prioritize Firestore, fallback to Google auth)
  const getDisplayName = () => {
    return userProfile?.displayName || user?.displayName || user?.email?.split('@')[0] || 'User';
  };

  // Get the best email (prioritize Firestore, fallback to Google auth)
  const getEmail = () => {
    return userProfile?.email || user?.email || '';
  };

  // Handle avatar image error
  const handleAvatarError = (event: React.SyntheticEvent<HTMLImageElement, Event>) => {
    // Hide the image so fallback shows
    event.currentTarget.style.display = 'none';
  };

  return (
    <>
      <style>{gradientAnimation}</style>
      <header className="fixed top-0 left-0 right-0 z-50 bg-background/95 backdrop-blur-md shadow-md py-2 border-b border-border">
        <div className="container mx-auto px-4">
          <div className="flex items-center justify-between h-14">
            {/* Left Section - Logo */}
            <div className="flex items-center w-[200px]">
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Link
                      to="/"
                      className="flex items-center gap-2 text-primary font-bold text-xl group cursor-pointer"
                    >
                      <div className="w-10 h-10 relative">
                        <div
                          className="absolute inset-0 bg-gradient-to-r from-primary via-purple-500 to-blue-500 opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-full animate-gradient"
                          style={{
                            backgroundSize: "200% 200%",
                            animation: "gradientAnimation 3s ease infinite",
                          }}
                        />
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          xmlnsXlink="http://www.w3.org/1999/xlink"
                          zoomAndPan="magnify"
                          viewBox="0 0 375 374.999991"
                          preserveAspectRatio="xMidYMid meet"
                          version="1.0"
                          className="text-foreground relative z-10 group-hover:text-white transition-colors duration-300"
                        >
                          <defs>
                            <clipPath id="844921de3f">
                              <path
                                d="M 61.789062 67.796875 L 313.039062 67.796875 L 313.039062 307.046875 L 61.789062 307.046875 Z M 61.789062 67.796875 "
                                clipRule="nonzero"
                              />
                            </clipPath>
                          </defs>
                          <g clipPath="url(#844921de3f)">
                            <path
                              fill="currentColor"
                              d="M 308.871094 147.816406 C 295.269531 97.726562 254.121094 67.820312 198.792969 67.820312 L 61.792969 67.820312 L 64.496094 74.015625 C 76.105469 100.628906 102.378906 117.835938 131.421875 117.84375 L 168.726562 117.84375 L 169.03125 117.835938 C 170.449219 117.734375 175.6875 117.414062 183.546875 117.414062 C 191.105469 117.414062 198.804688 117.714844 206.40625 118.296875 L 207.152344 118.351562 C 236.257812 120.644531 276.136719 128.253906 300.777344 151.449219 L 301.09375 151.746094 C 301.222656 151.867188 301.351562 151.988281 301.476562 152.113281 L 313.203125 163.792969 Z M 312.625 205.542969 L 312.589844 206.023438 C 305.191406 268.433594 261.117188 307.199219 197.570312 307.199219 L 110.171875 307.199219 L 110.171875 189.042969 L 114.59375 189.042969 C 140.179688 189.042969 160.996094 209.859375 160.996094 235.449219 L 160.996094 256.367188 L 199.707031 256.367188 C 218.769531 256.367188 234.824219 250.105469 246.140625 238.261719 C 257.652344 226.203125 263.738281 208.777344 263.738281 187.871094 L 263.738281 187.152344 C 263.738281 170.960938 260.175781 157.003906 253.152344 145.652344 L 246.597656 135.0625 L 258.367188 139.144531 C 272.96875 144.210938 284.8125 150.960938 293.566406 159.210938 C 306.363281 171.261719 312.773438 186.847656 312.625 205.542969 "
                              fillOpacity="1"
                              fillRule="nonzero"
                            />
                          </g>
                        </svg>
                      </div>
                    </Link>
                  </TooltipTrigger>
                  <TooltipContent
                    side="bottom"
                    className="bg-primary text-primary-foreground z-[60]"
                  >
                    <p>DevShowcase</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>

            {/* Center Section - Desktop Navigation */}
            <nav className="hidden md:flex items-center justify-center flex-1 max-w-[600px] mx-auto">
              <div className="flex items-center space-x-1">
                <Link to="/" className={cn(getLinkClasses("/"), "cursor-pointer")}>
                  <div className="flex items-center gap-2">
                    <Home className="w-5 h-5" />
                    <span className="hidden lg:inline">Home</span>
                  </div>
                </Link>
                <Link
                  to="/Portfolios"
                  className={cn(getLinkClasses("/Portfolios"), "cursor-pointer")}
                >
                  <div className="flex items-center gap-2">
                    <Briefcase className="w-5 h-5" />
                    <span className="hidden lg:inline">Portfolios</span>
                  </div>
                </Link>
              </div>
            </nav>

            {/* Right Section - Actions */}
            <div className="hidden md:flex items-center gap-2 w-[200px] justify-end">
              <Button
                variant="ghost"
                size="icon"
                className="rounded-full cursor-pointer"
                onClick={() => setIsSearchOpen(true)}
              >
                <Search className="w-5 h-5" />
              </Button>
              <div className="cursor-pointer relative z-[60]">
                <ModeToggle />
              </div>
              {user ? (
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="ghost" size="icon" className="rounded-full p-0 h-10 w-10 hover:bg-primary/10 transition-colors">
                      <Avatar className="h-10 w-10 border-2 border-border hover:border-primary transition-colors">
                        <AvatarImage 
                          src={getPhotoURL()} 
                          alt={getDisplayName()}
                          onError={handleAvatarError}
                          className="object-cover"
                        />
                        <AvatarFallback className="bg-primary text-primary-foreground text-sm font-medium">
                          {getUserInitials()}
                        </AvatarFallback>
                      </Avatar>
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent align="end" className="w-56 z-[60]">
                    <div className="flex items-center gap-2 p-2">
                      <Avatar className="h-8 w-8 border border-border">
                        <AvatarImage 
                          src={getPhotoURL()} 
                          alt={getDisplayName()}
                          onError={handleAvatarError}
                          className="object-cover"
                        />
                        <AvatarFallback className="bg-primary text-primary-foreground text-xs font-medium">
                          {getUserInitials()}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex flex-col space-y-1">
                        <p className="text-sm font-medium leading-none">
                          {getDisplayName()}
                        </p>
                        <p className="text-xs leading-none text-muted-foreground">
                          {getEmail()}
                        </p>
                      </div>
                    </div>
                    {/* Admin Panel link for admins */}
                    {userProfile?.isAdmin && (
                      <DropdownMenuItem asChild>
                        <Link to="/admin-panel">
                          <Shield className="w-4 h-4 mr-2 text-purple-500" />
                          Admin Panel
                        </Link>
                      </DropdownMenuItem>
                    )}
                    <DropdownMenuItem asChild>
                      <Link to={userProfile?.username ? `/${userProfile.username}` : "/create-profile"}>
                        <User className="w-4 h-4 mr-2" />
                        Profile
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem asChild>
                      <Link to="/update-profile">
                        <Settings className="w-4 h-4 mr-2" />
                        Settings
                      </Link>
                    </DropdownMenuItem>
                    <DropdownMenuItem onClick={handleSignOut}>
                      <LogOut className="w-4 h-4 mr-2" />
                      Sign out
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              ) : (
                <Button 
                  variant="outline" 
                  className="flex items-center gap-2"
                  onClick={handleSignIn}
                  disabled={loading}
                >
                  {loading ? 'Signing in...' : 'Sign in'}
                </Button>
              )}
            </div>

            {/* Mobile Menu Button */}
            <div className="flex items-center gap-3 md:hidden">
              <Button
                variant="ghost"
                size="icon"
                className="rounded-full cursor-pointer"
                onClick={() => setIsSearchOpen(true)}
              >
                <Search className="w-5 h-5" />
              </Button>
              <Button
                variant="ghost"
                size="icon"
                className="rounded-full cursor-pointer"
                onClick={() => setIsMenuOpen(!isMenuOpen)}
              >
                {isMenuOpen ? (
                  <X className="w-5 h-5" />
                ) : (
                  <Menu className="w-5 h-5" />
                )}
              </Button>
            </div>
          </div>
        </div>
      </header>

      {/* Mobile Menu */}
      {isMenuOpen && (
        <div
          className="md:hidden fixed inset-0 z-40 bg-background/95 backdrop-blur-md pt-[4.5rem]"
          onClick={() => setIsMenuOpen(false)}
        >
          <div
            className="container mx-auto px-4 py-4"
            onClick={(e) => e.stopPropagation()}
          >
            <nav className="flex flex-col space-y-2">
              <div className="mb-4"></div>
              <Link
                to="/"
                className={cn(getLinkClasses("/", true), "cursor-pointer")}
                onClick={() => setIsMenuOpen(false)}
              >
                <div className="flex items-center gap-2">
                  <Home className="w-5 h-5" />
                  <span>Home</span>
                </div>
              </Link>
              <Link
                to="/Portfolios"
                className={cn(getLinkClasses("/Portfolios", true), "cursor-pointer")}
                onClick={() => setIsMenuOpen(false)}
              >
                <div className="flex items-center gap-2">
                  <Briefcase className="w-5 h-5" />
                  <span>Portfolios</span>
                </div>
              </Link>
              {user && (
                <>
                  {/* Admin Panel link for admins (mobile) */}
                  {userProfile?.isAdmin && (
                    <Link
                      to="/admin-panel"
                      className={cn(getLinkClasses("/admin-panel", true), "cursor-pointer")}
                      onClick={() => setIsMenuOpen(false)}
                    >
                      <div className="flex items-center gap-2">
                        <Shield className="w-5 h-5 text-purple-500" />
                        <span>Admin Panel</span>
                      </div>
                    </Link>
                  )}
                </>
              )}
              <div className="pt-4 border-t border-border"></div>
              <div className="flex items-center justify-between px-4 py-2">
                <div className="cursor-pointer relative z-[60]">
                  <ModeToggle />
                </div>
                {user ? (
                  <div className="flex items-center gap-2">
                    <Avatar className="h-8 w-8 border border-border">
                      <AvatarImage 
                        src={getPhotoURL()} 
                        alt={getDisplayName()}
                        onError={handleAvatarError}
                        className="object-cover"
                      />
                      <AvatarFallback className="bg-primary text-primary-foreground text-xs font-medium">
                        {getUserInitials()}
                      </AvatarFallback>
                    </Avatar>
                    <Button 
                      variant="outline" 
                      className="flex items-center gap-2"
                      onClick={handleSignOut}
                      disabled={loading}
                    >
                      <LogOut className="w-4 h-4" />
                      Sign out
                    </Button>
                  </div>
                ) : (
                  <Button 
                    variant="outline" 
                    className="flex items-center gap-2"
                    onClick={handleSignIn}
                    disabled={loading}
                  >
                    {loading ? 'Signing in...' : 'Sign in'}
                  </Button>
                )}
              </div>
            </nav>
          </div>
        </div>
      )}

      {/* Search Modal */}
      <Dialog open={isSearchOpen} onOpenChange={setIsSearchOpen}>
        <DialogContent className="sm:max-w-[500px] max-h-[80vh] overflow-hidden">
          <DialogHeader>
            <DialogTitle>Search Users</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground w-4 h-4" />
              <Input
                placeholder="Search by name (starts with)..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
                autoFocus
              />
            </div>
            
            <div className="max-h-[400px] overflow-y-auto">
              {isLoadingUsers ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
                  <span className="ml-2 text-muted-foreground">Loading users...</span>
                </div>
              ) : searchResults.length > 0 ? (
                <div className="space-y-2">
                  {searchResults.map((user) => (
                    <div
                      key={user.uid}
                      className="flex items-center gap-3 p-3 rounded-lg border border-border hover:bg-accent/50 cursor-pointer transition-colors"
                      onClick={() => handleSearchResultClick(user.username)}
                    >
                      <Avatar className="h-10 w-10 border border-border">
                        <AvatarImage 
                          src={user.photoURL || undefined} 
                          alt={user.displayName}
                          className="object-cover"
                        />
                        <AvatarFallback className="bg-primary text-primary-foreground text-sm font-medium">
                          {user.displayName.split(' ').map(name => name[0]).join('').toUpperCase().slice(0, 2)}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <p className="font-medium text-sm truncate">{user.displayName}</p>
                          {user.isVerified && (
                            <span className="text-primary text-xs">✓</span>
                          )}
                        </div>
                        <p className="text-xs text-muted-foreground truncate">@{user.username}</p>
                        {user.role && (
                          <p className="text-xs text-muted-foreground truncate">{user.role}</p>
                        )}
                      </div>
                      <ExternalLink className="w-4 h-4 text-muted-foreground" />
                    </div>
                  ))}
                </div>
              ) : searchQuery.trim() ? (
                <div className="text-center py-8 text-muted-foreground">
                  <Search className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p>No users found</p>
                </div>
              ) : (
                <div className="text-center py-8 text-muted-foreground">
                  <Search className="w-8 h-8 mx-auto mb-2 opacity-50" />
                  <p>Start typing to search users</p>
                  <p className="text-xs mt-1">Searching through {allUsers.length} users</p>
                </div>
              )}
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
