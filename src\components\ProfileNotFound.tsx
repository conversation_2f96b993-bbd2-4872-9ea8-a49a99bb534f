import { DotLottieReact } from "@lottiefiles/dotlottie-react";
import { Link, useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { MagicCard } from "@/components/magicui/magic-card";
import { useAuth } from "@/contexts/AuthContext";
import { AlertTriangle } from "lucide-react";

const ProfileNotFound = ({
  error,
  username,
}: {
  error?: string | null;
  username?: string;
}) => {
  const { user } = useAuth();
  const navigate = useNavigate();

  return (
    <div className="flex items-center justify-center min-h-screen bg-background text-foreground">
      <MagicCard
        className="w-full max-w-lg p-8 text-center rounded-2xl flex flex-col items-center"
        gradientFrom="#f43f5e" // rose-500
        gradientTo="#be123c" // rose-700
        gradientSize={400}
      >
        <div className="relative mx-auto h-56 w-56 mb-4">
          <DotLottieReact
            src="/notFound/Animation - 1750448974926.json"
            loop
            autoplay
          />
        </div>
        <h1 className="mt-4 text-4xl font-bold tracking-tight text-gray-900 dark:text-gray-50 sm:text-5xl flex items-center gap-3 justify-center">
          <AlertTriangle className="h-10 w-10 text-rose-500" />
          Profile Not Found
        </h1>
        <p className="mt-4 text-lg text-gray-600 dark:text-gray-400">
          We couldn't find a profile for the username{" "}
          <strong className="text-rose-500 dark:text-rose-400">
            {username}
          </strong>
          .
        </p>
        <p className="mt-2 text-base text-gray-500 dark:text-gray-500">
          {error ||
            "This user may have changed their username, or the profile does not exist."}
        </p>
        <div className="mt-8 flex gap-4">
          {user ? (
            <Button asChild>
              <Link to="/create-profile">Create Your Profile</Link>
            </Button>
          ) : (
            <Button onClick={() => navigate("/")}>Go Home to Sign In</Button>
          )}
          <Button onClick={() => navigate(-1)} variant="outline">
            Go Back
          </Button>
        </div>
      </MagicCard>
    </div>
  );
};

export default ProfileNotFound; 