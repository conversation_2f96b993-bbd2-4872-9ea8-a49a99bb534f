import React, { useState, useEffect } from "react";
import { useParams } from 'react-router-dom';
import { useUserByUsername } from '@/hooks/useFirebaseCache';
import { Card } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { MapPin, Globe, Github, Twitter, Linkedin, Mail, Calendar, ExternalLink, ChevronDown, CheckCircle, Database, Server, Layout, Terminal, Cloud, Box, BookMarked, GitFork, Star } from 'lucide-react';
import { MagicCard } from '@/components/magicui/magic-card';
import {
  SiReact,
  SiNextdotjs,
  SiTypescript,
  SiJavascript,
  SiPython,
  SiNodedotjs,
  SiVuedotjs,
  SiAngular,
  SiHtml5,
  SiCss3,
  SiTailwindcss,
  SiMongodb,
  SiPostgresql,
  SiMysql,
  SiDocker,
  SiKubernetes,
  SiGooglecloud,
  SiGit,
  SiPhp,
  SiLaravel,
  SiDjango,
  SiFlask,
  SiRedis,
  SiGraphql,
  SiFirebase,
  SiVercel,
  SiNetlify,
  SiHeroku,
  SiDigitalocean,
  SiBootstrap,
  SiSass,
  SiWebpack,
  SiVite,
  SiJest,
  SiCypress,
  SiSelenium,
  SiExpress,
  SiCloudflare,
  SiAstro,
  SiWebstorm,
  SiPostman,
  SiRemix,
  SiPnpm,
  SiMui,
} from "react-icons/si";
import { FaAws, FaJava, FaFigma, FaLinux, FaNpm } from "react-icons/fa";
import { VscAzure } from "react-icons/vsc";
import { RiSvelteFill } from "react-icons/ri";
import { FcGoogle } from "react-icons/fc";
import { IoRocketOutline } from "react-icons/io5";
import { Skeleton } from '@/components/ui/skeleton';
import ProfileNotFound from "@/components/ProfileNotFound";
import GitHubCalendar from 'react-github-calendar';
import { getLanguageColor } from "@/lib/language-colors";

interface Repo {
  id: number;
  name: string;
  html_url: string;
  description: string;
  language: string;
  stargazers_count: number;
  forks_count: number;
}

// Tech icon mapping function
const getTechIcon = (techName: string) => {
  const normalizedTech = techName.toLowerCase();

  const iconMap: { [key: string]: React.ReactNode } = {
    react: <SiReact className="h-5 w-5 text-[#61DAFB] dark:text-[#61DAFB]" />,
    next: <SiNextdotjs className="h-5 w-5 text-black dark:text-white" />,
    typescript: <SiTypescript className="h-5 w-5 text-[#3178C6] dark:text-[#3178C6]" />,
    javascript: <SiJavascript className="h-5 w-5 text-[#F7DF1E] dark:text-[#F7DF1E]" />,
    python: <SiPython className="h-5 w-5 text-[#3776AB] dark:text-[#3776AB]" />,
    nodejs: <SiNodedotjs className="h-5 w-5 text-[#339933] dark:text-[#339933]" />,
    node: <SiNodedotjs className="h-5 w-5 text-[#339933] dark:text-[#339933]" />,
    vue: <SiVuedotjs className="h-5 w-5 text-[#4FC08D] dark:text-[#4FC08D]" />,
    svelte: <RiSvelteFill className="h-5 w-5 text-[#FF3E00] dark:text-[#FF3E00]" />,
    angular: <SiAngular className="h-5 w-5 text-[#DD0031] dark:text-[#DD0031]" />,
    html: <SiHtml5 className="h-5 w-5 text-[#E34F26] dark:text-[#E34F26]" />,
    css: <SiCss3 className="h-5 w-5 text-[#1572B6] dark:text-[#1572B6]" />,
    tailwind: <SiTailwindcss className="h-5 w-5 text-[#06B6D4] dark:text-[#06B6D4]" />,
    mongodb: <SiMongodb className="h-5 w-5 text-[#47A248] dark:text-[#47A248]" />,
    postgresql: <SiPostgresql className="h-5 w-5 text-[#4169E1] dark:text-[#4169E1]" />,
    mysql: <SiMysql className="h-5 w-5 text-[#4479A1] dark:text-[#4479A1]" />,
    docker: <SiDocker className="h-5 w-5 text-[#2496ED] dark:text-[#2496ED]" />,
    kubernetes: <SiKubernetes className="h-5 w-5 text-[#326CE5] dark:text-[#326CE5]" />,
    aws: <FaAws className="h-5 w-5 text-[#FF9900] dark:text-[#FF9900]" />,
    "google cloud": <SiGooglecloud className="h-5 w-5 text-[#4285F4] dark:text-[#4285F4]" />,
    azure: <VscAzure className="h-5 w-5 text-[#0078D4] dark:text-[#0078D4]" />,
    git: <SiGit className="h-5 w-5 text-[#F05032] dark:text-[#F05032]" />,
    java: <FaJava className="h-5 w-5 text-[#007396] dark:text-[#007396]" />,
    php: <SiPhp className="h-5 w-5 text-[#777BB4] dark:text-[#777BB4]" />,
    laravel: <SiLaravel className="h-5 w-5 text-[#FF2D20] dark:text-[#FF2D20]" />,
    django: <SiDjango className="h-5 w-5 text-[#092E20] dark:text-[#092E20]" />,
    flask: <SiFlask className="h-5 w-5 text-white dark:text-white" />,
    redis: <SiRedis className="h-5 w-5 text-[#DC382D] dark:text-[#DC382D]" />,
    graphql: <SiGraphql className="h-5 w-5 text-[#E10098] dark:text-[#E10098]" />,
    firebase: <SiFirebase className="h-5 w-5 text-[#FFCA28] dark:text-[#FFCA28]" />,
    vercel: <SiVercel className="h-5 w-5 text-white dark:text-white" />,
    netlify: <SiNetlify className="h-5 w-5 text-[#00C7B7] dark:text-[#00C7B7]" />,
    heroku: <SiHeroku className="h-5 w-5 text-[#430098] dark:text-[#430098]" />,
    digitalocean: <SiDigitalocean className="h-5 w-5 text-[#0080FF] dark:text-[#0080FF]" />,
    bootstrap: <SiBootstrap className="h-5 w-5 text-[#7952B3] dark:text-[#7952B3]" />,
    sass: <SiSass className="h-5 w-5 text-[#CC6699] dark:text-[#CC6699]" />,
    webpack: <SiWebpack className="h-5 w-5 text-[#8DD6F9] dark:text-[#8DD6F9]" />,
    vite: <SiVite className="h-5 w-5 text-[#646CFF] dark:text-[#646CFF]" />,
    jest: <SiJest className="h-5 w-5 text-[#C21325] dark:text-[#C21325]" />,
    cypress: <SiCypress className="h-5 w-5 text-[#17202C] dark:text-[#17202C]" />,
    selenium: <SiSelenium className="h-5 w-5 text-[#43B02A] dark:text-[#43B02A]" />,
    database: <Database className="h-5 w-5 text-indigo-400 dark:text-indigo-400" />,
    backend: <Server className="h-5 w-5 text-indigo-400 dark:text-indigo-400" />,
    frontend: <Layout className="h-5 w-5 text-indigo-400 dark:text-indigo-400" />,
    cli: <Terminal className="h-5 w-5 text-indigo-400 dark:text-indigo-400" />,
    cloud: <Cloud className="h-5 w-5 text-indigo-400 dark:text-indigo-400" />,
    figma: <FaFigma className="h-5 w-5 text-indigo-400 dark:text-indigo-400" />,
    astro: <SiAstro className="h-5 w-5 text-[#F7DF1E] dark:text-[#F7DF1E]" />,
    rest: <IoRocketOutline className="h-5 w-5 text-red-400 dark:text-red-400" />,
    websockets: <SiWebstorm className="h-5 w-5 text-indigo-400 dark:text-indigo-400" />,
    linux: <FaLinux className="h-5 w-5 text-yellow-400 dark:text-yellow-400" />,
    postman: <SiPostman className="h-5 w-5 text-orange-400 dark:text-orange-400" />,
    remix: <SiRemix className="h-5 w-5 text-white dark:text-white" />,
    npm: <FaNpm className="h-5 w-5 text-red-400 dark:text-red-400" />,
    pnpm: <SiPnpm className="h-5 w-5 text-[#F1E05A] dark:text-[#F1E05A]" />,
    "material ui": <SiMui className="h-5 w-5 text-[#0081CB] dark:text-[#0081CB]" />,
    express: <SiExpress className="h-5 w-5 text-[#000000] dark:text-white" />,
    cloudflare: <SiCloudflare className="h-5 w-5 text-[#F38020] dark:text-[#F38020]" />,
    google: <FcGoogle className="h-5 w-5 text-[#4285F4] dark:text-[#4285F4]" />,
    default: <Box className="h-5 w-5 text-indigo-400 dark:text-indigo-400" />,
  };

  // Try to find an exact match first
  if (iconMap[normalizedTech]) {
    return iconMap[normalizedTech];
  }

  // If no exact match, try to find a partial match
  const partialMatch = Object.keys(iconMap).find(
    (key) => normalizedTech.includes(key) || key.includes(normalizedTech)
  );

  return partialMatch ? iconMap[partialMatch] : iconMap.default;
};

// Helper components
function SocialLink({
  href,
  label,
  icon,
}: {
  href: string;
  label: string;
  icon: React.ReactNode;
}) {
  const getBgColor = (label: string) => {
    const normalizedLabel = label.toLowerCase();
    if (normalizedLabel.includes("github"))
      return "bg-gray-100 dark:bg-gray-800 group-hover:bg-gray-200 dark:group-hover:bg-gray-700";
    if (normalizedLabel.includes("linkedin"))
      return "bg-[#0077B5]/10 group-hover:bg-[#0077B5]/20 dark:bg-[#0077B5]/20 dark:group-hover:bg-[#0077B5]/30";
    if (normalizedLabel.includes("twitter"))
      return "bg-[#1DA1F2]/10 group-hover:bg-[#1DA1F2]/20 dark:bg-[#1DA1F2]/20 dark:group-hover:bg-[#1DA1F2]/30";
    if (normalizedLabel.includes("portfolio"))
      return "bg-primary/10 group-hover:bg-primary/20 dark:bg-primary/20 dark:group-hover:bg-primary/30";
    if (normalizedLabel.includes("mail") || normalizedLabel.includes("email"))
      return "bg-red-100 group-hover:bg-red-200 dark:bg-red-500/20 dark:group-hover:bg-red-500/30";
    return "bg-gray-100 dark:bg-gray-800 group-hover:bg-gray-200 dark:group-hover:bg-gray-700";
  };

  return (
    <a
      href={href}
      target="_blank"
      rel="noopener noreferrer"
      className="flex items-center gap-3 text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white transition-colors duration-200 group"
    >
      <div
        className={`flex items-center justify-center w-10 h-10 rounded-xl ${getBgColor(
          label
        )} transition-colors duration-200`}
      >
        <div className="text-primary">{icon}</div>
      </div>
      <span className="font-medium">{label}</span>
      <ExternalLink className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity ml-auto" />
    </a>
  );
}

function RepoCard({ repo }: { repo: Repo }) {

  return (
    <Card className="p-4 bg-white/60 dark:bg-black/60 backdrop-blur-xl border border-gray-200/50 dark:border-white/10 rounded-2xl shadow-lg h-full flex flex-col transition-all duration-300 hover:shadow-xl hover:scale-[1.02]">
      <div className="flex items-center mb-3">
        <BookMarked className="h-4 w-4 mr-2 text-gray-600 dark:text-gray-400" />
        <a
          href={repo.html_url}
          target="_blank"
          rel="noopener noreferrer"
          className="font-bold text-blue-600 dark:text-blue-400 hover:underline truncate"
        >
          {repo.name}
        </a>
      </div>
      <p className="text-sm text-gray-600 dark:text-gray-300 mb-4 flex-grow">
        {repo.description || "No description provided."}
      </p>
      <div className="flex items-center text-xs text-gray-500 dark:text-gray-400 mt-auto">
        {repo.language && (
          <div className="flex items-center mr-4">
            <span
              className="w-3 h-3 rounded-full mr-1.5"
              style={{ backgroundColor: getLanguageColor(repo.language) }}
            ></span>
            {repo.language}
          </div>
        )}
        <div className="flex items-center mr-4">
          <Star className="h-4 w-4 mr-1 text-yellow-500" />
          {repo.stargazers_count}
        </div>
        <div className="flex items-center">
          <GitFork className="h-4 w-4 mr-1 text-gray-600 dark:text-gray-400" />
          {repo.forks_count}
        </div>
      </div>
    </Card>
  );
}

function StatItem({ label, value }: { label: string; value: string | number }) {
  return (
    <div className="text-center p-3 rounded-xl bg-gray-100/60 dark:bg-black backdrop-blur-sm border border-gray-200/50 dark:border-white/10">
      <div className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
        {value}
      </div>
      <div className="text-xs text-gray-500 dark:text-gray-400 uppercase tracking-wider font-medium">
        {label}
      </div>
    </div>
  );
}

function ProjectCard({
  project,
  className = "",
}: {
  project: any;
  className?: string;
}) {
  const { title, description, technologies, url, imageUrl } = project;
  const [isExpanded, setIsExpanded] = useState(false);

  return (
    <MagicCard
      className={`group relative bg-white/60 dark:bg-black/60 backdrop-blur-xl border border-gray-200/50 dark:border-white/10 rounded-2xl shadow-xl overflow-hidden flex flex-col h-[460px] transition-all duration-300 ${className}`}
    >
      {/* Image area (fixed height) */}
      <div className="relative w-full h-44 bg-gray-100 dark:bg-gray-900 flex items-center justify-center overflow-hidden">
        {imageUrl ? (
          <>
            <img
              src={imageUrl}
              alt={title}
              className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-110"
              onError={(e) => {
                e.currentTarget.style.display = 'none';
              }}
            />
            <div className="absolute top-3 right-3 z-20">
              <Badge className="bg-white/90 dark:bg-black/90 backdrop-blur-sm text-gray-700 dark:text-gray-200 text-xs font-medium px-3 py-1.5 border border-gray-200/50 dark:border-white/10 shadow-lg">
                {technologies?.[0] || 'Project'}
              </Badge>
            </div>
          </>
        ) : (
          <div className="flex items-center justify-center w-full h-full text-gray-400 dark:text-gray-600 text-lg font-semibold">
            No Image
          </div>
        )}
      </div>

      {/* Card content */}
      <div className="flex-1 flex flex-col px-6 py-5">
        <h3 className="text-xl font-bold mb-2 text-gray-900 dark:text-white group-hover:text-blue-600 dark:group-hover:text-blue-400 transition-all duration-300 overflow-hidden text-ellipsis" style={{ display: '-webkit-box', WebkitLineClamp: 2, WebkitBoxOrient: 'vertical' }}>
          {title}
        </h3>
        <div className="relative mb-3 flex-1">
          <p className={`text-gray-600 dark:text-gray-300 text-sm leading-relaxed transition-all duration-300 ${!isExpanded ? 'overflow-hidden text-ellipsis' : ''}`} style={!isExpanded ? { display: '-webkit-box', WebkitLineClamp: 2, WebkitBoxOrient: 'vertical' } : {}}>
            {description}
          </p>
          {description.length > 120 && (
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="mt-1 text-xs font-medium text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-200 flex items-center gap-1"
            >
              {isExpanded ? (
                <>
                  Show less
                  <ChevronDown className="h-3 w-3 rotate-180 transition-transform duration-200" />
                </>
              ) : (
                <>
                  Read more
                  <ChevronDown className="h-3 w-3 transition-transform duration-200" />
                </>
              )}
            </button>
          )}
        </div>
        {technologies && technologies.length > 0 && (
          <div className="flex flex-wrap gap-2 mb-4">
            {technologies.slice(0, 4).map((tech: string) => (
              <Badge
                key={tech}
                className="bg-white/80 dark:bg-black/80 backdrop-blur-sm text-gray-700 dark:text-gray-200 text-xs font-medium px-2.5 py-1 border border-gray-200/50 dark:border-white/10 shadow-sm hover:bg-white dark:hover:bg-black transition-all duration-200 hover:scale-105"
              >
                {tech}
              </Badge>
            ))}
            {technologies.length > 4 && (
              <Badge className="bg-white/80 dark:bg-black/80 backdrop-blur-sm text-gray-700 dark:text-gray-200 text-xs font-medium px-2.5 py-1 border border-gray-200/50 dark:border-white/10 shadow-sm">
                +{technologies.length - 4} more
              </Badge>
            )}
          </div>
        )}
        <div className="mt-auto pt-2">
          {url && (
            <a
              href={url}
              target="_blank"
              rel="noopener noreferrer"
              className="w-full flex items-center justify-center gap-2 px-4 py-2 rounded-xl border-2 border-blue-600 dark:border-blue-400 text-blue-600 dark:text-blue-400 bg-transparent hover:bg-blue-50 dark:hover:bg-blue-900/20 font-semibold shadow-none hover:shadow-lg transition-all duration-300 focus:outline-none focus:ring-2 focus:ring-blue-400"
            >
              <Globe className="h-5 w-5" />
              <span>View Project</span>
              <ExternalLink className="h-4 w-4 opacity-0 group-hover:opacity-100 transition-opacity" />
            </a>
          )}
        </div>
      </div>
    </MagicCard>
  );
}

export default function Profile() {
  const { username } = useParams();
  // Use cached hook for better performance
  const { data: userProfile, isLoading: loading, error } = useUserByUsername(username);
  const [repos, setRepos] = useState<Repo[]>([]);
  const [reposLoading, setReposLoading] = useState(true);
  const [showMoreRepos, setShowMoreRepos] = useState(false);

  const githubUsername = userProfile?.githubUrl?.split("/").pop();

  useEffect(() => {
    if (githubUsername) {
      setReposLoading(true);
      fetch(`https://api.github.com/users/${githubUsername}/repos?per_page=100`)
        .then((res) => {
          if (!res.ok) {
            throw new Error('Failed to fetch repos');
          }
          return res.json();
        })
        .then((data) => {
          if (Array.isArray(data)) {
            const sortedRepos = data.sort(
              (a, b) => b.stargazers_count - a.stargazers_count
            );
            setRepos(sortedRepos);
          }
        })
        .catch((err) => {
          console.error("Error fetching GitHub repos:", err);
          setRepos([]);
        })
        .finally(() => {
          setReposLoading(false);
        });
    } else if (!loading) {
      setReposLoading(false);
      setRepos([]);
    }
  }, [githubUsername, loading]);

  const visibleRepos = repos.slice(0, showMoreRepos ? 8 : 4);

  if (loading) {
    return (
      <div className="text-gray-800 dark:text-white overflow-hidden relative min-h-screen">
        <div className="fixed inset-0 pointer-events-none overflow-hidden">
          {/* Top grid and light overlays */}
          <div
            className="absolute top-0 left-0 w-full h-[35%] bg-[linear-gradient(to_right,#00000008_1px,transparent_1px),linear-gradient(to_bottom,#00000008_1px,transparent_1px)] dark:bg-[linear-gradient(to_right,#ffffff08_1px,transparent_1px),linear-gradient(to_bottom,#ffffff08_1px,transparent_1px)] bg-[size:25px_25px] opacity-70"
            style={{
              maskImage:
                "linear-gradient(to bottom, rgba(0,0,0,0.9) 0%, transparent 100%)",
            }}
          ></div>
          <div
            className="absolute top-0 left-0 w-full h-[25%] bg-[linear-gradient(to_right,#00000010_1px,transparent_1px),linear-gradient(to_bottom,#00000010_1px,transparent_1px)] dark:bg-[linear-gradient(to_right,#ffffff10_1px,transparent_1px),linear-gradient(to_bottom,#ffffff10_1px,transparent_1px)] bg-[size:80px_80px] opacity-50"
            style={{
              maskImage:
                "linear-gradient(to bottom, rgba(0,0,0,0.7) 0%, transparent 100%)",
            }}
          ></div>
          {/* Subtle top lighting effects */}
          <div className="absolute top-0 left-[25%] w-[50%] h-[20rem] bg-gradient-to-b from-blue-400/10 to-transparent dark:from-blue-400/10 dark:to-transparent blur-3xl"></div>
          <div className="absolute top-0 right-[20%] w-[30%] h-[10rem] bg-gradient-to-b from-purple-400/10 to-transparent dark:from-purple-400/10 dark:to-transparent blur-3xl"></div>
          {/* Light effects to complement top grid */}
          <div className="absolute left-0 top-0 h-1/3 w-full bg-gradient-to-b from-blue-500/5 via-transparent to-transparent dark:from-blue-400/5 dark:via-transparent"></div>
          {/* Horizontal lines near top */}
          <div className="absolute top-[15%] left-[10%] w-[80%] h-[1px] bg-gradient-to-r from-transparent via-gray-400/20 to-transparent dark:via-gray-400/10"></div>
          <div className="absolute top-[25%] left-[5%] w-[90%] h-[1px] bg-gradient-to-r from-transparent via-gray-400/10 to-transparent dark:via-gray-400/5"></div>
        </div>
        <section className="relative flex flex-col items-center justify-between py-8 lg:py-16 max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
          <div className="grid grid-cols-1 items-center md:grid-cols-2 lg:gap-16 gap-y-12 w-full">
            {/* Left: Avatar and basic info skeleton */}
            <Card className="flex flex-col items-center justify-center rounded-lg p-3 lg:py-5 lg:px-12 border-2 border-gray-200/30 dark:border-white/5 h-full mx-auto bg-white/60 dark:bg-black/60 backdrop-blur-xl">
              <div className="flex w-full justify-center">
                <Skeleton className="w-32 h-32 md:w-36 md:h-36 rounded-full" />
              </div>
              <Skeleton className="h-7 w-40 mt-4 rounded" />
              <Skeleton className="h-5 w-24 mt-2 rounded" />
              <Skeleton className="h-6 w-48 mt-3 rounded" />
              <div className="flex flex-col items-center w-full mt-3 mb-3">
                <Skeleton className="h-8 w-48 rounded-xl" />
                <Skeleton className="h-4 w-24 mt-2 rounded" />
              </div>
              <Skeleton className="h-4 w-32 mt-2 rounded" />
              <Skeleton className="h-10 w-32 mt-6 rounded-full" />
            </Card>
            {/* Right: Code card skeleton */}
            <Card className="h-full w-full max-w-2xl mx-auto bg-gray-50 dark:from-[#000000] border-gray-200/50 dark:border-[#1b2c68a0] relative rounded-lg border dark:bg-gradient-to-r dark:to-[#0a0d37] shadow-lg">
              <div className="flex flex-row">
                <div className="h-[2px] w-full bg-gradient-to-r from-transparent via-pink-500 to-violet-600"></div>
                <div className="h-[2px] w-full bg-gradient-to-r from-violet-600 to-transparent"></div>
              </div>
              <div className="px-4 lg:px-8 py-5 flex justify-between items-center bg-gray-100 dark:bg-[#000000]">
                <div className="flex flex-row space-x-2">
                  <Skeleton className="h-3 w-3 rounded-full bg-red-400" />
                  <Skeleton className="h-3 w-3 rounded-full bg-orange-300" />
                  <Skeleton className="h-3 w-3 rounded-full bg-green-400" />
                </div>
                <Skeleton className="h-4 w-20 rounded" />
              </div>
              <div className="overflow-hidden border-t-[2px] border-gray-300 dark:border-indigo-900 px-4 lg:px-8 py-4 lg:py-8 relative">
                <div className="absolute -top-24 -left-24 w-56 h-56 bg-blue-600 rounded-full opacity-5 dark:opacity-10 filter blur-3xl"></div>
                <div className="absolute -bottom-24 -right-24 w-56 h-56 bg-pink-600 rounded-full opacity-5 dark:opacity-10 filter blur-3xl"></div>
                <div className="relative flex">
                  <div className="hidden md:flex flex-col items-end pr-4 text-gray-400 dark:text-gray-500 font-mono text-xs">
                    {Array.from({ length: 12 }).map((_, i) => (
                      <Skeleton key={i} className="h-4 w-8 mb-1 rounded" />
                    ))}
                  </div>
                  <div className="flex-1 space-y-2">
                    <Skeleton className="h-5 w-40 rounded" />
                    <Skeleton className="h-5 w-32 rounded" />
                    <Skeleton className="h-5 w-24 rounded" />
                    <Skeleton className="h-5 w-48 rounded" />
                    <Skeleton className="h-5 w-36 rounded" />
                  </div>
                </div>
                <div className="mt-6 border-t border-gray-300 dark:border-gray-800 pt-3 text-xs text-gray-500 flex justify-between items-center">
                  <Skeleton className="h-4 w-10 rounded" />
                  <Skeleton className="h-4 w-16 rounded" />
                  <Skeleton className="h-4 w-12 rounded" />
                </div>
              </div>
            </Card>
          </div>
        </section>
        {/* About and Skills Section Skeleton */}
        <section className="max-w-screen-xl mx-auto px-4 sm:px-6 py-8">
          <div className="grid grid-cols-1 md:grid-cols-12 gap-12">
            <div className="md:col-span-5 lg:col-span-4 space-y-8">
              <Card className="bg-gray-50 dark:bg-black backdrop-blur-md rounded-2xl p-6 border border-gray-200/50 dark:border-white/10 shadow-lg">
                <Skeleton className="h-6 w-32 mb-4 rounded" />
                <div className="space-y-4">
                  <Skeleton className="h-4 w-40 rounded" />
                  <Skeleton className="h-4 w-32 rounded" />
                  <Skeleton className="h-4 w-28 rounded" />
                  <Skeleton className="h-4 w-36 rounded" />
                </div>
              </Card>
              <Card className="bg-gray-50 dark:bg-black backdrop-blur-md rounded-2xl p-6 border border-gray-200/50 dark:border-white/10">
                <Skeleton className="h-6 w-32 mb-4 rounded" />
                <div className="space-y-3">
                  <Skeleton className="h-4 w-24 rounded" />
                  <Skeleton className="h-4 w-32 rounded" />
                </div>
              </Card>
              <Card className="bg-gray-50 dark:bg-black backdrop-blur-md rounded-2xl p-6 border border-gray-200/50 dark:border-white/10">
                <Skeleton className="h-6 w-32 mb-4 rounded" />
                <div className="grid grid-cols-2 gap-4">
                  <Skeleton className="h-12 w-20 rounded-xl" />
                  <Skeleton className="h-12 w-20 rounded-xl" />
                  <Skeleton className="h-12 w-20 rounded-xl" />
                  <Skeleton className="h-12 w-20 rounded-xl" />
                </div>
              </Card>
            </div>
            <div className="md:col-span-7 lg:col-span-8 space-y-8">
              <Card className="bg-gray-50 dark:bg-black backdrop-blur-md rounded-2xl p-8 border border-gray-200/50 dark:border-white/10 mb-12">
                <Skeleton className="h-8 w-48 mb-6 rounded" />
                <Skeleton className="h-4 w-full mb-2 rounded" />
                <Skeleton className="h-4 w-2/3 mb-2 rounded" />
                <Skeleton className="h-4 w-1/2 mb-2 rounded" />
              </Card>
              <Card className="bg-gray-50 dark:bg-[#0a0a0a] backdrop-blur-md rounded-2xl p-8 border border-gray-200 dark:border-gray-800 transition-colors">
                <Skeleton className="h-8 w-48 mb-6 rounded" />
                <div className="flex flex-wrap gap-3">
                  {Array.from({ length: 8 }).map((_, i) => (
                    <Skeleton key={i} className="h-8 w-24 rounded-full" />
                  ))}
                </div>
              </Card>
            </div>
          </div>
        </section>
        {/* Projects Section Skeleton */}
        <div className="max-w-screen-xl mx-auto px-4 sm:px-6 py-4 space-y-12">
          <section id="projects">
            <div className="relative w-full mb-12">
              <Card className="relative bg-gradient-to-br from-[#f8fafc] to-[#e0e7ef] dark:from-[#101828] dark:to-[#0a0f1c] rounded-2xl px-10 py-12 flex flex-col md:flex-row items-start md:items-center justify-between shadow-lg overflow-hidden">
                <Skeleton className="absolute left-10 top-8 h-20 w-32 rounded-xl opacity-20" />
                <div className="relative z-10 flex-1">
                  <Skeleton className="h-10 w-48 mb-2 rounded" />
                </div>
                <div className="relative z-10 flex flex-col items-end gap-2">
                  <Skeleton className="h-4 w-32 mb-2 rounded" />
                  <Skeleton className="h-4 w-24 rounded" />
                </div>
              </Card>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 justify-center place-items-center">
              {Array.from({ length: 3 }).map((_, i) => (
                <Card key={i} className="group relative bg-white/60 dark:bg-black/60 backdrop-blur-xl border border-gray-200/50 dark:border-white/10 rounded-2xl shadow-xl overflow-hidden flex flex-col h-[460px] transition-all duration-300">
                  <Skeleton className="w-full h-44" />
                  <div className="flex-1 flex flex-col px-6 py-5">
                    <Skeleton className="h-7 w-40 mb-2 rounded" />
                    <Skeleton className="h-4 w-full mb-3 rounded" />
                    <div className="flex flex-wrap gap-2 mb-4">
                      <Skeleton className="h-6 w-16 rounded-full" />
                      <Skeleton className="h-6 w-16 rounded-full" />
                      <Skeleton className="h-6 w-16 rounded-full" />
                      <Skeleton className="h-6 w-16 rounded-full" />
                    </div>
                    <div className="mt-auto pt-2">
                      <Skeleton className="h-10 w-full rounded-xl" />
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          </section>
        </div>
      </div>
    );
  }

  if (error || !userProfile) {
    return <ProfileNotFound error={error} username={username} />;
  }

  // Destructure all fields from userProfile
  const {
    photoURL,
    displayName,
    username: currentUsername,
    isVerified,
    seniorityLevel,
    role,
    githubUrl,
    linkedinUrl,
    twitterUrl,
    website,
    email,
    country,
    createdAt,
    joinDate,
    stats,
    bio,
    techStack,
    projects,
    availableForWork,
    workType,
    hourlyRate,
  } = userProfile;

  // Format join date
  const formatDate = (timestampOrString: number | string | undefined) => {
    if (!timestampOrString) return "N/A";
    try {
      const date = typeof timestampOrString === 'number' ? new Date(timestampOrString) : new Date(timestampOrString);
      return date.toLocaleDateString(undefined, { year: "numeric", month: "long", day: "numeric" });
    } catch {
      return "Invalid Date";
    }
  };

  return (
    <div className="text-gray-800 dark:text-white overflow-hidden relative min-h-screen">
      <div className="fixed inset-0 pointer-events-none overflow-hidden">
        {/* Top grid and light overlays */}
        <div
          className="absolute top-0 left-0 w-full h-[35%] bg-[linear-gradient(to_right,#00000008_1px,transparent_1px),linear-gradient(to_bottom,#00000008_1px,transparent_1px)] dark:bg-[linear-gradient(to_right,#ffffff08_1px,transparent_1px),linear-gradient(to_bottom,#ffffff08_1px,transparent_1px)] bg-[size:25px_25px] opacity-70"
          style={{
            maskImage:
              "linear-gradient(to bottom, rgba(0,0,0,0.9) 0%, transparent 100%)",
          }}
        ></div>
        <div
          className="absolute top-0 left-0 w-full h-[25%] bg-[linear-gradient(to_right,#00000010_1px,transparent_1px),linear-gradient(to_bottom,#00000010_1px,transparent_1px)] dark:bg-[linear-gradient(to_right,#ffffff10_1px,transparent_1px),linear-gradient(to_bottom,#ffffff10_1px,transparent_1px)] bg-[size:80px_80px] opacity-50"
          style={{
            maskImage:
              "linear-gradient(to bottom, rgba(0,0,0,0.7) 0%, transparent 100%)",
          }}
        ></div>
        {/* Subtle top lighting effects */}
        <div className="absolute top-0 left-[25%] w-[50%] h-[20rem] bg-gradient-to-b from-blue-400/10 to-transparent dark:from-blue-400/10 dark:to-transparent blur-3xl"></div>
        <div className="absolute top-0 right-[20%] w-[30%] h-[10rem] bg-gradient-to-b from-purple-400/10 to-transparent dark:from-purple-400/10 dark:to-transparent blur-3xl"></div>
        {/* Light effects to complement top grid */}
        <div className="absolute left-0 top-0 h-1/3 w-full bg-gradient-to-b from-blue-500/5 via-transparent to-transparent dark:from-blue-400/5 dark:via-transparent"></div>
        {/* Horizontal lines near top */}
        <div className="absolute top-[15%] left-[10%] w-[80%] h-[1px] bg-gradient-to-r from-transparent via-gray-400/20 to-transparent dark:via-gray-400/10"></div>
        <div className="absolute top-[25%] left-[5%] w-[90%] h-[1px] bg-gradient-to-r from-transparent via-gray-400/10 to-transparent dark:via-gray-400/5"></div>
      </div>
      <section className="relative flex flex-col items-center justify-between py-8 lg:py-16 max-w-7xl mx-auto px-4 sm:px-6 md:px-8">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-y-8 md:gap-x-8 lg:gap-x-16 w-full min-w-0">
          <div className="flex flex-col items-center justify-center w-full max-w-xl mx-auto min-w-0 px-2 sm:px-0">
            <div className="relative w-28 h-28 sm:w-32 sm:h-32 md:w-36 md:h-36">
              <img
                src={photoURL || `https://avatar.vercel.sh/${currentUsername || displayName || "user"}.svg?text=${displayName ? displayName.charAt(0).toUpperCase() : "U"}`}
                alt={displayName || "User Avatar"}
                className="rounded-full transition-all duration-1000 grayscale hover:grayscale-0 hover:scale-110 cursor-pointer w-full h-full object-cover border border-gray-200 dark:border-gray-700"
              />
            </div>

            {displayName && (
              <h2 className="text-3xl md:text-4xl font-bold mt-4 text-gray-800 dark:text-white text-center flex items-center justify-center gap-2">
                {displayName}
                {isVerified && (
                  <span className="inline-block align-bottom relative top-[2px]">
                    <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 48 48">
                      <polygon fill="#42a5f5" points="29.62,3 33.053,8.308 39.367,8.624 39.686,14.937 44.997,18.367 42.116,23.995 45,29.62 39.692,33.053 39.376,39.367 33.063,39.686 29.633,44.997 24.005,42.116 18.38,45 14.947,39.692 8.633,39.376 8.314,33.063 3.003,29.633 5.884,24.005 3,18.38 8.308,14.947 8.624,8.633 14.937,8.314 18.367,3.003 23.995,5.884"></polygon>
                      <polygon fill="#fff" points="21.396,31.255 14.899,24.76 17.021,22.639 21.428,27.046 30.996,17.772 33.084,19.926"></polygon>
                    </svg>
                  </span>
                )}
              </h2>
            )}

            {bio && (
              <p className="text-gray-600 dark:text-gray-300 text-base my-4 text-center max-w-xl line-clamp-3">
                {bio}
              </p>
            )}

            <div className="flex flex-wrap items-center justify-center gap-x-6 gap-y-2 text-sm text-gray-600 dark:text-gray-400">
              {joinDate && (
                <div className="flex items-center gap-2">
                  <Calendar className="h-4 w-4" />
                  <span>Joined {formatDate(joinDate || createdAt)}</span>
                </div>
              )}
              {country && (
                <div className="flex items-center gap-2">
                  <MapPin className="h-4 w-4" />
                  <span>{country}</span>
                </div>
              )}
              {website && (
                <a href={website} target="_blank" rel="noopener noreferrer" className="flex items-center gap-2 hover:text-blue-500 transition-colors">
                  <Globe className="h-4 w-4" />
                  <span>{website.replace(/^(https?:\/\/)?(www\.)?/, '').replace(/\/$/, '')}</span>
                </a>
              )}
            </div>

            {techStack && techStack.length > 0 && (
              <div className="mt-8 flex flex-wrap justify-center gap-1.5">
                {techStack.map((tech: string) => (
                  <Badge
                    key={tech}
                    variant="secondary"
                    className="px-2.5 py-1 bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-200 border-0"
                  >
                    <div className="flex items-center gap-1.5">
                      <div className="flex-shrink-0 w-4 h-4 flex items-center justify-center">
                        {getTechIcon(tech)}
                      </div>
                      <span className="font-medium text-sm">{tech}</span>
                    </div>
                  </Badge>
                ))}
              </div>
            )}
          </div>
          <div className="h-full w-full max-w-2xl mx-auto bg-gray-50 dark:from-[#000000] border-gray-200/50 dark:border-[#1b2c68a0] relative rounded-lg border dark:bg-gradient-to-r dark:to-[#0a0d37] shadow-lg min-w-0 px-2 sm:px-0">
            <div className="flex flex-row">
              <div className="h-[2px] w-full bg-gradient-to-r from-transparent via-pink-500 to-violet-600"></div>
              <div className="h-[2px] w-full bg-gradient-to-r from-violet-600 to-transparent"></div>
            </div>
            <div className="px-4 lg:px-8 py-5 flex justify-between items-center bg-gray-100 dark:bg-[#000000]">
              <div className="flex flex-row space-x-2">
                <div className="h-3 w-3 rounded-full bg-red-500"></div>
                <div className="h-3 w-3 rounded-full bg-orange-400"></div>
                <div className="h-3 w-3 rounded-full bg-green-400"></div>
              </div>
              <div className="text-xs text-gray-500 dark:text-gray-400 font-mono">coder.js</div>
            </div>
            <div className="overflow-hidden border-t-[2px] border-gray-300 dark:border-indigo-900 px-4 lg:px-8 py-4 lg:py-8 relative">
              <div className="absolute -top-24 -left-24 w-56 h-56 bg-blue-600 rounded-full opacity-5 dark:opacity-10 filter blur-3xl"></div>
              <div className="absolute -bottom-24 -right-24 w-56 h-56 bg-pink-600 rounded-full opacity-5 dark:opacity-10 filter blur-3xl"></div>
              <div className="relative flex">
                <div className="hidden md:flex flex-col items-end pr-4 text-gray-400 dark:text-gray-500 font-mono text-xs">
                  {Array.from({ length: 12 }).map((_, i) => (
                    <div key={i} className="leading-relaxed select-none opacity-70">{i + 1}</div>
                  ))}
                </div>
                <code className="font-mono text-xs md:text-sm lg:text-base w-full relative">
                  <div><span className="mr-2 text-purple-700 dark:text-pink-400">const</span><span className="mr-2 text-blue-600 dark:text-violet-400">coder</span><span className="mr-2 text-purple-700 dark:text-pink-400">=</span><span className="text-gray-500 dark:text-gray-400">&#123;</span></div>
                  <div className="pl-6"><span className="text-gray-800 dark:text-white">name:</span><span className="text-gray-500 dark:text-gray-400">'</span><span className="text-emerald-600 dark:text-green-400">{displayName || "Your Name"}</span><span className="text-gray-500 dark:text-gray-400">',</span></div>
                  <div className="pl-6"><span className="text-gray-800 dark:text-white">role:</span><span className="text-gray-500 dark:text-gray-400">'</span><span className="text-emerald-600 dark:text-green-400">{role || "Software Developer"}</span><span className="text-gray-500 dark:text-gray-400">',</span></div>
                  {seniorityLevel && (<div className="pl-6"><span className="text-gray-800 dark:text-white">seniority:</span><span className="text-gray-500 dark:text-gray-400">'</span><span className="text-emerald-600 dark:text-green-400">{seniorityLevel}</span><span className="text-gray-500 dark:text-gray-400">',</span></div>)}
                  <div className="pl-6"><span className="text-gray-800 dark:text-white">location:</span><span className="text-gray-500 dark:text-gray-400">'</span><span className="text-emerald-600 dark:text-green-400">{country || "Bangladesh"}</span><span className="text-gray-500 dark:text-gray-400">',</span></div>
                  {stats &&
                    Object.values(stats).some(
                      (val) => val !== undefined && val !== null && typeof val === 'number' && val > 0
                    ) && (
                      <>
                        {stats.followers !== undefined && (
                          <div className="pl-6"><span className="text-gray-800 dark:text-white">followers:</span><span className="text-orange-400">{stats.followers}</span><span className="text-gray-500 dark:text-gray-400">,</span></div>
                        )}
                        {stats.following !== undefined && (
                          <div className="pl-6"><span className="text-gray-800 dark:text-white">following:</span><span className="text-orange-400">{stats.following}</span><span className="text-gray-500 dark:text-gray-400">,</span></div>
                        )}
                        {stats.projects !== undefined && (
                          <div className="pl-6"><span className="text-gray-800 dark:text-white">projects:</span><span className="text-orange-400">{stats.projects}</span><span className="text-gray-500 dark:text-gray-400">,</span></div>
                        )}
                      </>
                    )}
                  {email && (<div className="pl-6"><span className="text-gray-800 dark:text-white">email:</span><span className="text-gray-500 dark:text-gray-400">'</span><span className="text-emerald-600 dark:text-green-400">{email}</span><span className="text-gray-500 dark:text-gray-400">',</span></div>)}
                  {techStack && techStack.length > 0 && (<div className="pl-6"><span className="text-gray-800 dark:text-white">skills:</span><span className="text-gray-500 dark:text-gray-400">[</span><div className="pl-6 flex flex-wrap">{techStack.map((tech: string, index: number) => (<span key={tech} className="tech-item"><span className="text-gray-500 dark:text-gray-400">'</span><span className="text-cyan-400">{tech}</span><span className="text-gray-500 dark:text-gray-400">'</span>{index < techStack.length - 1 && (<span className="text-gray-500 dark:text-gray-400">, </span>)}</span>))}</div><div className="pl-0"><span className="text-gray-500 dark:text-gray-400">],</span></div></div>)}
                  {joinDate && (<div className="pl-6"><span className="text-gray-800 dark:text-white">joinedAt:</span><span className="text-gray-500 dark:text-gray-400">'</span><span className="text-emerald-600 dark:text-green-400">{formatDate(joinDate || createdAt)}</span><span className="text-gray-500 dark:text-gray-400">',</span></div>)}
                  <div><span className="text-gray-500 dark:text-gray-400">&#125;;</span></div>
                </code>
              </div>
              <div className="mt-6 border-t border-gray-300 dark:border-gray-800 pt-3 text-xs text-gray-500 flex justify-between items-center">
                <div>UTF-8</div>
                <div>JavaScript</div>
                <div>Ln 14, Col 2</div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="max-w-screen-xl mx-auto px-4 sm:px-6 py-8">
        <div className="grid grid-cols-1 md:grid-cols-12 gap-12">
          <div className="md:col-span-5 lg:col-span-4">
            <div className="sticky top-32">
              <div className="space-y-8">
                {/* Available for work section as its own card at the top */}
                {availableForWork && (
                  <div className="border border-green-200 dark:border-green-700 shadow-sm rounded-2xl p-6 flex flex-col items-center justify-center">
                    <div className="flex items-center gap-2 mb-1">
                      <CheckCircle className="h-5 w-5 text-green-500" />
                      <span className="text-green-700 dark:text-green-300 font-semibold text-lg">Available for work{workType ? `: ${workType}` : ''}</span>
                    </div>
                    {hourlyRate && (
                      <span className="text-green-600 dark:text-green-300 text-sm font-medium">USD {hourlyRate}/hr</span>
                    )}
                  </div>
                )}
                <div className="bg-gray-50 dark:bg-black backdrop-blur-md rounded-2xl p-6 border border-gray-200/50 dark:border-white/10 shadow-lg">
                  <h3 className="text-lg font-medium text-gray-900 dark:text-white/90 mb-4">
                    Connect
                  </h3>
                  <div className="space-y-4">
                    {website && (
                      <SocialLink
                        href={website}
                        label="Portfolio"
                        icon={<Globe className="h-5 w-5" />}
                      />
                    )}
                    {githubUrl && (
                      <SocialLink
                        href={githubUrl}
                        label="GitHub"
                        icon={<Github className="h-5 w-5" />}
                      />
                    )}
                    {linkedinUrl && (
                      <SocialLink
                        href={linkedinUrl}
                        label="LinkedIn"
                        icon={<Linkedin className="h-5 w-5" />}
                      />
                    )}
                    {twitterUrl && (
                      <SocialLink
                        href={twitterUrl}
                        label="Twitter"
                        icon={<Twitter className="h-5 w-5" />}
                      />
                    )}
                    {email && (
                      <SocialLink
                        href={`mailto:${email}`}
                        label={email}
                        icon={<Mail className="h-5 w-5" />}
                      />
                    )}
                  </div>
                </div>

                {(country || joinDate) && (
                  <div className="bg-gray-50 dark:bg-black backdrop-blur-md rounded-2xl p-6 border border-gray-200/50 dark:border-white/10">
                    <h3 className="text-lg font-medium text-gray-900 dark:text-white/90 mb-4">
                      Details
                    </h3>
                    <div className="space-y-3">
                      {country && (
                        <div className="flex items-center gap-3 text-gray-600 dark:text-gray-300">
                          <MapPin className="h-4 w-4 text-primary" />
                          <span>{country}</span>
                        </div>
                      )}
                      {joinDate && (
                        <div className="flex items-center gap-3 text-gray-600 dark:text-gray-300">
                          <Calendar className="h-4 w-4 text-primary" />
                          <span>Joined {formatDate(joinDate || createdAt)}</span>
                        </div>
                      )}
                    </div>
                  </div>
                )}

                {stats &&
                  Object.values(stats).some(
                    (val) => val !== undefined && val !== null && typeof val === 'number' && val > 0
                  ) && (
                    <div className="bg-gray-50 dark:bg-black backdrop-blur-md rounded-2xl p-6 border border-gray-200/50 dark:border-white/10">
                      <h3 className="text-lg font-medium text-gray-900 dark:text-white/90 mb-4">
                        Stats
                      </h3>
                      <div className="grid grid-cols-2 gap-4">
                        {stats.projects !== undefined && (
                          <StatItem label="Projects" value={stats.projects} />
                        )}
                        {stats.followers !== undefined && (
                          <StatItem label="Followers" value={stats.followers} />
                        )}
                        {stats.following !== undefined && (
                          <StatItem label="Following" value={stats.following} />
                        )}
                        {stats.contributions !== undefined && (
                          <StatItem
                            label="Contributions"
                            value={stats.contributions}
                          />
                        )}
                      </div>
                    </div>
                  )}
              </div>
            </div>
          </div>

          <div className="md:col-span-7 lg:col-span-8 space-y-8">
            <Card className="bg-gray-50 dark:bg-black backdrop-blur-md rounded-2xl p-8 border border-gray-200/50 dark:border-white/10 mb-12">
              <h3 className="text-2xl font-bold mb-6 text-gray-900 dark:text-white/90">
                Biography
              </h3>
              <div className="prose dark:prose-invert max-w-none">
                <p className="text-gray-600 dark:text-gray-300 leading-relaxed whitespace-pre-wrap">
                  {bio ||
                    "This user prefers to keep an air of mystery... no bio provided."}
                </p>
              </div>
            </Card>

            {/* GitHub Section - moved here above Technical Skills */}
            {githubUsername && (
              <div className="bg-gray-50 dark:bg-black backdrop-blur-md rounded-2xl p-8 border border-gray-200/50 dark:border-white/10 mb-12">
                <Card className="p-4 bg-white/60 dark:bg-black/60 backdrop-blur-xl border border-gray-200/50 dark:border-white/10 rounded-2xl shadow-lg mb-8 flex justify-center items-center">
                  <GitHubCalendar
                    username={githubUsername}
                    blockSize={10}
                    blockMargin={2}
                    fontSize={10}
                    theme={{
                      light: ['#ebedf0', '#9be9a8', '#40c463', '#30a14e', '#216e39'],
                      dark: ['#161b22', '#0e4429', '#006d32', '#26a641', '#39d353'],
                    }}
                  />
                </Card>
            
                <h4 className="text-xl font-bold mb-4 text-gray-800 dark:text-white">Top Repositories</h4>
                {reposLoading ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    {[...Array(4)].map((_, i) => (
                      <Card key={i} className="p-4 bg-white/60 dark:bg-black/60 backdrop-blur-xl border border-gray-200/50 dark:border-white/10 rounded-2xl shadow-lg h-32">
                        <Skeleton className="h-4 w-2/3 mb-3 rounded" />
                        <Skeleton className="h-3 w-full mb-2 rounded" />
                        <Skeleton className="h-3 w-full mb-4 rounded" />
                        <div className="flex items-center">
                          <Skeleton className="h-4 w-16 rounded" />
                          <Skeleton className="h-4 w-12 ml-4 rounded" />
                          <Skeleton className="h-4 w-12 ml-4 rounded" />
                        </div>
                      </Card>
                    ))}
                  </div>
                ) : repos.length > 0 ? (
                  <>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      {visibleRepos.map((repo) => (
                        <RepoCard key={repo.id} repo={repo} />
                      ))}
                    </div>
                    {repos.length > 4 && (
                      <div className="mt-6 text-center">
                        <button
                          onClick={() => setShowMoreRepos(!showMoreRepos)}
                          className="text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 transition-colors duration-200 flex items-center gap-1 mx-auto"
                        >
                          {showMoreRepos ? (
                            <>
                              Show less
                              <ChevronDown className="h-3 w-3 rotate-180 transition-transform duration-200" />
                            </>
                          ) : (
                            <>
                              Show more
                              <ChevronDown className="h-3 w-3 transition-transform duration-200" />
                            </>
                          )}
                        </button>
                      </div>
                    )}
                  </>
                ) : (
                  <div className="text-center py-8 text-gray-500 dark:text-gray-400">
                    No public repositories found.
                  </div>
                )}
              </div>
            )}

          </div>
        </div>
      </section>

      {/* Projects Section */}
      <div className="max-w-screen-xl mx-auto px-4 sm:px-6 py-4 space-y-12">
        {projects && projects.length > 0 && (
          <section id="projects">
            <div className="relative w-full mb-12">
              <div className="relative bg-gradient-to-br from-[#f8fafc] to-[#e0e7ef] dark:from-[#101828] dark:to-[#0a0f1c] rounded-2xl px-10 py-12 flex flex-col md:flex-row items-start md:items-center justify-between shadow-lg overflow-hidden">
                <span className="absolute left-10 top-8 text-[5rem] md:text-[7rem] font-extrabold text-gray-900/10 dark:text-white/10 select-none pointer-events-none z-0">
                  {projects.length.toString().padStart(2, "0")}
                </span>
                <div className="relative z-10 flex-1">
                  <h2 className="text-3xl md:text-4xl font-extrabold text-gray-900 dark:text-white mb-2">
                    Selected Works
                  </h2>
                </div>
                <div className="relative z-10 flex flex-col items-end gap-2">
                  <span className="text-gray-600 dark:text-gray-300 text-sm">
                    {new Date().getFullYear()} Collection
                  </span>
                  <span className="flex items-center gap-2 text-green-600 dark:text-green-400 text-sm">
                    <span className="h-2 w-2 rounded-full bg-green-600 dark:bg-green-400 inline-block"></span>
                    {projects.filter((p: any) => p.url).length} Live
                  </span>
                </div>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 justify-center place-items-center">
              {projects.map((project: any, index: number) => (
                <ProjectCard
                  key={`${project.title}-${index}`}
                  project={project}
                />
              ))}
            </div>
          </section>
        )}
      </div>

      {/* Footer with back to top */}
      <div className="relative mt-20">
        <div className="absolute inset-0 pointer-events-none">
          <div className="absolute inset-0 bg-[linear-gradient(to_right,#80808012_1px,transparent_1px),linear-gradient(to_bottom,#80808012_1px,transparent_1px)] bg-[size:16px_16px]"></div>
          <div className="absolute inset-0 bg-gradient-to-b from-transparent via-gray-100/50 to-gray-100 dark:via-black dark:to-black"></div>

          <div
            className="absolute bottom-0 left-[10%] w-[30%] h-[60%] opacity-40 bg-[linear-gradient(to_right,#00000010_1px,transparent_1px),linear-gradient(to_bottom,#00000010_1px,transparent_1px)] dark:bg-[linear-gradient(to_right,#ffffff10_1px,transparent_1px),linear-gradient(to_bottom,#ffffff10_1px,transparent_1px)] bg-[size:8px_8px]"
            style={{
              maskImage:
                "radial-gradient(circle at top right, rgba(0,0,0,0.8) 20%, transparent 70%)",
            }}
          ></div>

          <div
            className="absolute bottom-0 right-[10%] w-[30%] h-[60%] opacity-40 bg-[linear-gradient(to_right,#00000010_1px,transparent_1px),linear-gradient(to_bottom,#00000010_1px,transparent_1px)] dark:bg-[linear-gradient(to_right,#ffffff10_1px,transparent_1px),linear-gradient(to_bottom,#ffffff10_1px,transparent_1px)] bg-[size:8px_8px]"
            style={{
              maskImage:
                "radial-gradient(circle at top left, rgba(0,0,0,0.8) 20%, transparent 70%)",
            }}
          ></div>

          <div className="absolute bottom-[10%] left-1/2 -translate-x-1/2 w-48 h-48 rounded-full bg-gradient-to-br from-blue-400/5 to-purple-300/5 dark:from-blue-400/10 dark:to-purple-300/5 blur-3xl"></div>
        </div>

        <div className="fixed bottom-8 right-8 z-50">
          <a
            href="#"
            className="rounded-full flex items-center justify-center text-gray-600 dark:text-gray-300 border border-gray-200/50 dark:border-white/10 backdrop-blur-sm transition-all duration-300 hover:scale-110 group"
            aria-label="Back to top"
          >
            <ChevronDown className="h-6 w-6 rotate-180 transition-transform duration-300 group-hover:-translate-y-1" />
          </a>
        </div>
      </div>
    </div>
  );
}


