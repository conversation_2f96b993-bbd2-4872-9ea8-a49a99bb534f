import { createBrowserRouter, Navigate } from "react-router-dom";
import { lazy, Suspense } from "react";
import App from "../App";
import ProtectedRoute from "../components/ProtectedRoute";
import { Skeleton } from "@/components/ui/skeleton";

// Lazy load components for better performance
const Home = lazy(() => import("../pages/Home"));
const Portfolios = lazy(() => import("../pages/Portfolios"));
const CreateProfile = lazy(() => import("../pages/create-profile"));
const UpdateProfile = lazy(() => import("../pages/update-profile"));
const Profile = lazy(() => import("../pages/Profile"));
const Settings = lazy(() => import("../pages/Settings"));
const AdminPanel = lazy(() => import("../pages/admin-panel"));
const Payment = lazy(() => import("@/pages/payment"));
const PricingPage = lazy(() => import("@/pages/pricing-page"));
const NotFound = lazy(() => import("@/pages/notFound"));

// Loading component for Suspense fallback
const PageLoader = () => (
  <div className="min-h-screen bg-background flex items-center justify-center">
    <div className="space-y-4 w-full max-w-md mx-auto p-6">
      <Skeleton className="h-8 w-3/4 mx-auto" />
      <Skeleton className="h-4 w-full" />
      <Skeleton className="h-4 w-5/6" />
      <div className="grid grid-cols-2 gap-4 mt-8">
        <Skeleton className="h-32 w-full" />
        <Skeleton className="h-32 w-full" />
      </div>
    </div>
  </div>
);

// Wrapper component for lazy loaded routes
const LazyWrapper = ({ children }: { children: React.ReactNode }) => (
  <Suspense fallback={<PageLoader />}>
    {children}
  </Suspense>
);

const router = createBrowserRouter([
  {
    path: "/",
    element: <App />,
    children : [
        {
            path: "/",
            element: (
              <LazyWrapper>
                <Home />
              </LazyWrapper>
            )
        },
        {
            path: "/portfolios",
            element: (
              <LazyWrapper>
                <Portfolios />
              </LazyWrapper>
            )
        },
        {
            path: "/create-profile",
            element: (
              <LazyWrapper>
                <ProtectedRoute requireAuth={true}>
                  <CreateProfile />
                </ProtectedRoute>
              </LazyWrapper>
            )
        },
        {
            path: "/update-profile",
            element: (
              <LazyWrapper>
                <ProtectedRoute requireAuth={true} requireProfile={true}>
                  <UpdateProfile />
                </ProtectedRoute>
              </LazyWrapper>
            )
        },
        {
            path: "/profile",
            element: <Navigate to="/" replace />
        },
        {
            path: "/:username",
            element: (
              <LazyWrapper>
                <Profile />
              </LazyWrapper>
            )
        },
        {
            path: "/settings",
            element: (
              <LazyWrapper>
                <ProtectedRoute requireAuth={true} requireProfile={true}>
                  <Settings />
                </ProtectedRoute>
              </LazyWrapper>
            )
        },
        {
            path: "/admin-panel",
            element: (
              <LazyWrapper>
                <ProtectedRoute requireAdmin={true}>
                  <AdminPanel />
                </ProtectedRoute>
              </LazyWrapper>
            )
        },
        {
            path: "/payment",
            element: (
              <LazyWrapper>
                <ProtectedRoute requireAuth={true}>
                  <Payment />
                </ProtectedRoute>
              </LazyWrapper>
            )
        },
        {
            path: "/pricing",
            element: (
              <LazyWrapper>
                <ProtectedRoute requireAuth={true}>
                  <PricingPage />
                </ProtectedRoute>
              </LazyWrapper>
            )
        },
        {
            path: "*",
            element: (
              <LazyWrapper>
                <NotFound />
              </LazyWrapper>
            )
        }
    ]
  }
]);

export default router;
