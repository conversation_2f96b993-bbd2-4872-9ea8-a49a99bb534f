import { useState } from "react";
import { Button } from "@/components/ui/button";
import { CheckCircle, Sparkles, Zap, Shield, Users, ArrowRight } from "lucide-react";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";

export default function Pricing() {
  const [selectedPlan, setSelectedPlan] = useState<string | null>(null);
  const [showTaka, setShowTaka] = useState(false);

  const plans = [
    {
      id: "free",
      name: "Free",
      price: 0,
      takaPrice: 0,
      features: [
        "Basic profile visibility",
        "Standard search ranking",
        "Community access",
        "Email support",
      ],
      icon: Users,
      popular: false,
      cta: "Get Started",
      description: "Perfect for getting started",
    },
    {
      id: "6-months",
      name: "Pro",
      price: 1.64,
      takaPrice: 200,
      features: [
        "Featured profile for 6 months",
        "Priority search ranking",
        "Enhanced developer badge",
        "6-month verified badge",
        "Priority support",
        "Analytics dashboard",
      ],
      icon: Zap,
      popular: true,
      cta: "Buy Pro",
      description: "Most popular for developers",
    },
    {
      id: "yearly",
      name: "Business",
      price: 2.86,
      takaPrice: 350,
      features: [
        "Featured profile for 12 months",
        "Priority search ranking",
        "Enhanced developer badge",
        "Lifetime verified badge",
        "Best value for long-term visibility",
        "Advanced analytics",
        "Custom branding",
      ],
      icon: Shield,
      popular: false,
      cta: "Buy Business",
      description: "For teams and enterprises",
    },
  ];

  const handleSelectPlan = (planId: string) => {
    setSelectedPlan(planId);
  };

  const handleContinue = () => {
    if (selectedPlan) {
      window.location.href = `/payment?plan=${selectedPlan}`;
    }
  };

  const getCurrentPrice = (plan: any) => {
    return showTaka ? plan.takaPrice : plan.price;
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/50 dark:from-slate-950 dark:via-slate-900 dark:to-slate-950 relative overflow-hidden">
      {/* Enhanced Background Pattern */}
      <div className="absolute inset-0 bg-[linear-gradient(to_right,#e2e8f0_1px,transparent_1px),linear-gradient(to_bottom,#e2e8f0_1px,transparent_1px)] bg-[size:64px_64px] dark:bg-[linear-gradient(to_right,#1e293b_1px,transparent_1px),linear-gradient(to_bottom,#1e293b_1px,transparent_1px)] animate-[grid_30s_linear_infinite]" />
      
      {/* Radial Gradients for Depth */}
      <div className="absolute top-0 left-1/4 w-96 h-96 bg-blue-400/10 rounded-full blur-3xl animate-pulse" />
      <div className="absolute bottom-0 right-1/4 w-96 h-96 bg-indigo-400/10 rounded-full blur-3xl animate-pulse delay-1000" />

      <div className="container relative mx-auto py-24 px-4 sm:px-6 lg:px-8">
        {/* Header Section */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center gap-2 px-4 py-2 rounded-full bg-blue-50 dark:bg-blue-950/50 border border-blue-200 dark:border-blue-800 mb-8">
            <Sparkles className="w-4 h-4 text-blue-600 dark:text-blue-400" />
            <span className="text-sm font-semibold text-blue-700 dark:text-blue-300">
              Choose Your Plan
            </span>
          </div>
          
          <h1 className="text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-slate-900 via-blue-900 to-indigo-900 dark:from-white dark:via-blue-100 dark:to-indigo-100 bg-clip-text text-transparent leading-tight">
            Pricing that scales
            <br />
            <span className="text-blue-600 dark:text-blue-400">with your career</span>
          </h1>
          
          <p className="text-xl text-slate-600 dark:text-slate-400 max-w-3xl mx-auto mb-12 leading-relaxed">
            Choose the perfect plan to boost your developer profile and unlock new opportunities. 
            All plans include our core features with no hidden fees.
          </p>

          {/* Currency Toggle */}
          <div className="inline-flex items-center justify-center space-x-3 bg-white dark:bg-slate-800/50 rounded-2xl p-2 shadow-lg border border-slate-200 dark:border-slate-700 backdrop-blur-sm">
            <Label
              htmlFor="currency-toggle"
              className={`text-sm font-semibold px-4 py-2 rounded-xl transition-all duration-200 cursor-pointer ${
                !showTaka
                  ? "bg-blue-600 text-white shadow-lg"
                  : "text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-200"
              }`}
            >
              USD
            </Label>
            <Switch
              id="currency-toggle"
              checked={showTaka}
              onCheckedChange={setShowTaka}
              className="data-[state=checked]:bg-blue-600"
            />
            <Label
              htmlFor="currency-toggle"
              className={`text-sm font-semibold px-4 py-2 rounded-xl transition-all duration-200 cursor-pointer ${
                showTaka
                  ? "bg-blue-600 text-white shadow-lg"
                  : "text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-slate-200"
              }`}
            >
              Taka
            </Label>
          </div>
        </div>

        {/* Pricing Cards */}
        <div className="grid lg:grid-cols-3 gap-6 max-w-5xl mx-auto mb-16">
          {plans.map((plan) => {
            const IconComponent = plan.icon;
            const currentPrice = getCurrentPrice(plan);
            
            return (
              <div
                key={plan.id}
                className={`relative group ${
                  plan.popular ? 'lg:scale-105' : ''
                }`}
              >
                <div
                  className={`h-full bg-white dark:bg-slate-800/50 rounded-2xl p-5 border transition-all duration-300 hover:shadow-xl hover:-translate-y-1.5 ${
                    selectedPlan === plan.id
                      ? "border-blue-500 shadow-md shadow-blue-500/20"
                      : plan.popular
                      ? "border-blue-200 dark:border-blue-700 shadow-md"
                      : "border-slate-200 dark:border-slate-700 hover:border-slate-300 dark:hover:border-slate-600"
                  } backdrop-blur-sm ${plan.popular ? 'mt-4' : ''}`}
                >
                  <div className="text-center mb-5">
                    <div className={`inline-flex items-center justify-center w-10 h-10 rounded-xl mb-4 ${
                      plan.popular 
                        ? "bg-gradient-to-br from-blue-500 to-indigo-600 text-white" 
                        : "bg-slate-100 dark:bg-slate-700 text-slate-600 dark:text-slate-400"
                    }`}>
                      <IconComponent className="w-5 h-5" />
                    </div>
                    
                    <h3 className="text-lg font-bold text-slate-900 dark:text-white mb-1">
                      {plan.name}
                    </h3>
                    
                    <p className="text-slate-600 dark:text-slate-400 mb-3 text-sm">
                      {plan.description}
                    </p>
                    
                    <div className="mb-3">
                      <div className="text-2xl lg:text-3xl font-bold text-slate-900 dark:text-white">
                        {currentPrice === 0 ? "Free" : `${showTaka ? "৳" : "$"}${currentPrice}`}
                      </div>
                      {currentPrice > 0 && (
                        <div className="text-slate-500 dark:text-slate-400 text-xs mt-0.5">
                          one-time
                        </div>
                      )}
                    </div>
                  </div>

                  <ul className="space-y-2 mb-5">
                    {plan.features.map((feature, index) => (
                      <li key={index} className="flex items-start gap-2">
                        <CheckCircle className="w-4 h-4 text-green-500 flex-shrink-0 mt-0.5" />
                        <span className="text-slate-700 dark:text-slate-300 leading-snug text-sm">
                          {feature}
                        </span>
                      </li>
                    ))}
                  </ul>

                  <Button
                    onClick={() => handleSelectPlan(plan.id)}
                    className={`w-full py-2.5 text-sm font-semibold rounded-xl transition-all duration-200 ${
                      selectedPlan === plan.id
                        ? "bg-blue-600 text-white shadow-md shadow-blue-600/20"
                        : plan.popular
                        ? "bg-gradient-to-r from-blue-600 to-indigo-600 text-white hover:from-blue-700 hover:to-indigo-700 shadow-md"
                        : "bg-slate-100 dark:bg-slate-700 text-slate-900 dark:text-white hover:bg-slate-200 dark:hover:bg-slate-600"
                    }`}
                  >
                    {selectedPlan === plan.id ? (
                      <span className="flex items-center gap-1.5">
                        Selected <CheckCircle className="w-4 h-4" />
                      </span>
                    ) : (
                      <span className="flex items-center gap-1.5">
                        {plan.cta} <ArrowRight className="w-4 h-4" />
                      </span>
                    )}
                  </Button>
                </div>
              </div>
            );
          })}
        </div>

        {/* Continue Button */}
        {selectedPlan && (
          <div className="text-center">
            <Button
              size="lg"
              onClick={handleContinue}
              className="text-lg py-6 px-12 bg-gradient-to-r from-blue-600 to-indigo-600 text-white hover:from-blue-700 hover:to-indigo-700 rounded-2xl shadow-xl shadow-blue-600/25 transition-all duration-300 transform hover:scale-105"
            >
              Continue with {plans.find((p) => p.id === selectedPlan)?.name}
            </Button>
          </div>
        )}
      </div>
    </div>
  );
}
