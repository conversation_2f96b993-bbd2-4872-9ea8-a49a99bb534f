import * as React from "react"
import { cn } from "@/lib/utils"
import { ChevronDown } from "lucide-react"

interface SelectProps {
  value?: string
  onValueChange?: (value: string) => void
  children: React.ReactNode
  className?: string
  placeholder?: string
}

const Select = React.forwardRef<HTMLDivElement, SelectProps>(
  ({ className, children, value, onValueChange, placeholder, ...props }, _ref) => {
    const [isOpen, setIsOpen] = React.useState(false)
    const selectRef = React.useRef<HTMLDivElement>(null)

    React.useEffect(() => {
      const handleClickOutside = (event: MouseEvent) => {
        if (selectRef.current && !selectRef.current.contains(event.target as Node)) {
          setIsOpen(false)
        }
      }

      document.addEventListener('mousedown', handleClickOutside)
      return () => document.removeEventListener('mousedown', handleClickOutside)
    }, [])

    const handleSelect = (selectedValue: string) => {
      if (selectedValue === "") return;
      onValueChange?.(selectedValue)
      setIsOpen(false)
    }

    const selectedOption = React.Children.toArray(children).find(
      (child) => React.isValidElement(child) && (child.props as any)?.value === value
    )

    return (
      <div ref={selectRef} className="relative">
        <div
          className={cn(
            "flex h-10 w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:cursor-not-allowed disabled:opacity-50 cursor-pointer",
            className
          )}
          onClick={() => setIsOpen(!isOpen)}
          {...props}
        >
          <span className={value ? "text-foreground" : "text-muted-foreground"}>
            {value ? (selectedOption as any)?.props?.children : placeholder}
          </span>
          <ChevronDown className={cn("h-4 w-4 transition-transform", isOpen && "rotate-180")} />
        </div>
        
        {isOpen && (
          <div className="absolute top-full left-0 right-0 z-50 mt-1 max-h-60 overflow-auto rounded-md border border-input bg-background shadow-lg">
            {React.Children.map(children, (child) => {
              if (React.isValidElement(child)) {
                const childProps = child.props as any;
                const isPlaceholder = childProps.value === "";
                return React.cloneElement(child as any, {
                  onClick: () => handleSelect(childProps.value),
                  className: cn(
                    "block w-full px-3 py-2 text-sm cursor-pointer hover:bg-accent hover:text-accent-foreground",
                    value === childProps.value && "bg-accent text-accent-foreground",
                    isPlaceholder && "text-muted-foreground cursor-default opacity-60",
                    childProps.className
                  ),
                  style: isPlaceholder ? { pointerEvents: 'none' } : undefined
                })
              }
              return child
            })}
          </div>
        )}
      </div>
    )
  }
)
Select.displayName = "Select"

const SelectItem = React.forwardRef<HTMLDivElement, React.HTMLAttributes<HTMLDivElement> & { value: string }>(
  ({ className, children, value, ...props }, ref) => {
    return (
      <div
        ref={ref}
        className={cn("", className)}
        {...props}
      >
        {children}
      </div>
    )
  }
)
SelectItem.displayName = "SelectItem"

export { Select, SelectItem } 