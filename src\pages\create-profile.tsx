import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { getAuth } from 'firebase/auth';
import { getFirestore, doc, setDoc, getDoc, collection, getDocs } from 'firebase/firestore';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectItem,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Check, X, Plus, Trash2 } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useFormValidation } from '@/hooks/useFormValidation';
import type { User, Project } from '@/types/user';
import { Combobox } from '@/components/ui/combobox';
import { countries as countryList } from '@/components/ui/country-select';

const techStackOptions = [
  // ... (same as before, omitted for brevity)
  'React', 'Next.js', 'Vue.js', 'Nuxt.js', 'Angular', 'Svelte', 'SolidJS', 'Alpine.js', 'jQuery', 'SvelteKit', 'Astro', 'SolidStart', 'Qwik', 'Remix',
  'Node.js', 'Express', 'NestJS', 'Fastify', 'Django', 'Flask', 'Ruby on Rails', 'Spring Boot', 'ASP.NET',
  'JavaScript', 'TypeScript', 'Python', 'Java', 'C#', 'C', 'C++', 'Go', 'Rust', 'PHP', 'Swift', 'Kotlin', 'Dart', 'Bash', 'SQL', 'R',
  'TailwindCSS', 'SCSS', 'CSS', 'HTML', 'Less', 'Bootstrap', 'Material UI', 'Chakra UI',
  'MongoDB', 'PostgreSQL', 'MySQL', 'SQLite', 'Firebase', 'Supabase', 'Redis', 'Oracle', 'Microsoft SQL Server',
  'AWS', 'Azure', 'Google Cloud', 'Docker', 'Kubernetes', 'Vercel', 'Netlify', 'Heroku', 'DigitalOcean', 'Nginx', 'Apache',
  'Git', 'GitHub', 'GitLab', 'Bitbucket',
  'Figma', 'Adobe XD', 'Photoshop', 'Illustrator', 'After Effects', 'Sketch', 'InVision',
  'GraphQL', 'REST', 'gRPC', 'WebSockets', 'Apollo', 'Axios', 'Postman',
  'Jest', 'Mocha', 'Chai', 'Cypress', 'Playwright', 'Testing Library', 'Selenium',
  'React Native', 'Flutter', 'Ionic', 'SwiftUI', 'Xamarin',
  'Webpack', 'Vite', 'Parcel', 'Rollup', 'Babel', 'NPM', 'Yarn', 'PNPM',
  'GitHub Actions', 'GitLab CI', 'CircleCI', 'Travis CI', 'Jenkins',
  'Linux', 'Command Line', 'Markdown', 'WebAssembly', 'Electron', 'Three.js', 'Unity', 'Blender',
];
const roleOptions = [
  'Frontend Developer', 'Backend Developer', 'MERN Developer', 'Full-Stack Developer', 'Mobile App Developer', 'DevOps Engineer', 'Software Engineer', 'QA Engineer / Tester', 'Game Developer', 'Data Scientist / Data Analyst', 'Machine Learning Engineer',
];
const seniorityOptions = [
  'Junior', 'Mid-Level', 'Senior', 'Lead', 'Principal', 'Architect', 'Other',
];
const bannerOptions = [
  { id: 'code', url: '/photos/code.jpg', name: 'Code' },
  { id: 'futuristic-laptop', url: '/photos/a-futuristic-laptop-displays.jpg', name: 'Futuristic Laptop' },
  { id: 'programming-desk', url: '/photos/programming-desk.jpg', name: 'Programming Desk' },
  { id: 'wallpaper', url: '/photos/wallpaper.jpg', name: 'Wallpaper' },
];

export default function CreateProfile() {
  const [loading, setLoading] = useState(false);
  const [showProjectForm, setShowProjectForm] = useState(false);
  const [existingUsers, setExistingUsers] = useState<User[]>([]);
  const [currentProject, setCurrentProject] = useState<Project>({ id: '', title: '', description: '', url: '', imageUrl: '', technologies: [] });
  const [usernameTimeout, setUsernameTimeout] = useState<NodeJS.Timeout | null>(null);

  const {
    formData,
    setFormData,
    isValidating,
    handleChange,
    handleSelectChange,
    handleTechStackChange,
    validateAll,
    clearError,
    errors,
    setErrors,
  } = useFormValidation<User>(
    {
      displayName: '',
      username: '',
      bio: '👨‍💻 Passionate developer with a love for building clean, efficient, and user-friendly applications. Always learning, always coding',
      country: '',
      role: '',
      seniorityLevel: '',
      website: '',
      githubUrl: '',
      twitterUrl: '',
      linkedinUrl: '',
      techStack: [],
      projects: [],
      bannerURL: '/photos/programming-desk.jpg',
      uid: '',
      email: '',
      photoURL: '',
      createdAt: 0,
      updatedAt: 0,
      isVerified: false,
      isFeatured : false,
      isAdmin: false,
    },
    existingUsers
  );

  const navigate = useNavigate();
  const auth = getAuth();
  const firestore = getFirestore();

  useEffect(() => {
    const fetchExistingUsers = async () => {
      try {
        const usersCol = collection(firestore, 'users');
        const usersSnapshot = await getDocs(usersCol);
        const users: User[] = usersSnapshot.docs.map(doc => doc.data() as User);
        setExistingUsers(users);
      } catch (error) {
        // Handle error silently
      }
    };
    fetchExistingUsers();
  }, [firestore]);

  useEffect(() => {
    const checkUserProfile = async () => {
      const user = auth.currentUser;
      if (!user) {
        navigate('/');
        return;
      }
      try {
        const userRef = doc(firestore, `users`, user.uid);
        const snapshot = await getDoc(userRef);
        if (snapshot.exists()) {
          navigate('/');
        }
      } catch (error) {
        // Handle error silently
      }
    };
    const unsubscribe = auth.onAuthStateChanged((user) => {
      if (user) checkUserProfile();
      else navigate('/');
    });
    return () => unsubscribe();
  }, [auth, firestore, navigate]);

  useEffect(() => {
    if (formData.username) {
      if (usernameTimeout) clearTimeout(usernameTimeout);
      const timeout = setTimeout(() => {
        const username = formData.username.toLowerCase();
        const isUsernameTaken = existingUsers.some(
          (user) => user.username?.toLowerCase() === username
        );
        if (isUsernameTaken) {
          setErrors((prev) => [
            ...prev.filter((err) => err.field !== 'username'),
            { field: 'username', message: 'This username is already taken', type: 'error' },
          ]);
        } else {
          setErrors((prev) => [
            ...prev.filter((err) => err.field !== 'username'),
            { field: 'username', message: 'Username is available', type: 'success' },
          ]);
        }
      }, 500);
      setUsernameTimeout(timeout);
    }
    return () => { if (usernameTimeout) clearTimeout(usernameTimeout); };
  }, [formData.username, existingUsers]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    const isValid = await validateAll();
    if (!isValid) {
      return;
    }
    setLoading(true);
    try {
      const user = auth.currentUser;
      if (!user) throw new Error('No user found');
      const formattedData = {
        ...formData,
        bannerURL: formData.bannerURL || '/photos/programming-desk.jpg',
        githubUrl: formData.githubUrl ? `https://github.com/${formData.githubUrl}` : '',
        twitterUrl: formData.twitterUrl ? `https://twitter.com/${formData.twitterUrl}` : '',
        linkedinUrl: formData.linkedinUrl ? `https://linkedin.com/in/${formData.linkedinUrl}` : '',
        uid: user.uid,
        email: user.email,
        photoURL: user.photoURL,
        createdAt: Date.now(),
        updatedAt: Date.now(),
        isVerified: false,
        isAdmin: false,
      };
      await setDoc(doc(firestore, 'users', user.uid), formattedData);
      await setDoc(doc(firestore, 'usernames', formData.username.toLowerCase()), { uid: user.uid });
      navigate('/portfolios');
    } catch (error) {
      // Handle error silently
    } finally {
      setLoading(false);
    }
  };

  const removeTechStack = (value: string) => {
    setFormData((prev: any) => ({ ...prev, techStack: prev.techStack.filter((item: string) => item !== value) }));
  };
  const handleProjectChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setCurrentProject((prev) => ({ ...prev, [name]: value }));
  };
  const handleProjectTechStackChange = (value: string) => {
    setCurrentProject((prev) => ({
      ...prev,
      technologies: prev.technologies.includes(value)
        ? prev.technologies.filter((item: string) => item !== value)
        : [...prev.technologies, value],
    }));
  };
  const removeProjectTech = (value: string) => {
    setCurrentProject((prev) => ({
      ...prev,
      technologies: prev.technologies.filter((item: string) => item !== value),
    }));
  };
  const validateProjects = (projects: Project[]) => {
    if (projects.length > 5) {
      return { field: 'projects', message: 'You can add up to 5 projects', type: 'error' };
    }
    return null;
  };
  const addProject = () => {
    if (!currentProject.title || !currentProject.description) return;
    setFormData((prev: any) => {
      const newProjects = [...prev.projects, { ...currentProject, id: Date.now().toString() }];
      const projectsError = validateProjects(newProjects);
      if (projectsError) {
        setErrors((prev: any) => [
          ...prev.filter((err: any) => err.field !== 'projects'),
          projectsError,
        ]);
      } else {
        setErrors((prev: any) => prev.filter((err: any) => err.field !== 'projects'));
      }
      return { ...prev, projects: newProjects };
    });
    setCurrentProject({ id: '', title: '', description: '', url: '', imageUrl: '', technologies: [] });
    setShowProjectForm(false);
  };
  const removeProject = (projectId: string) => {
    setFormData((prev: any) => ({ ...prev, projects: prev.projects.filter((project: Project) => project.id !== projectId) }));
  };
  const getFieldError = (field: string) => errors.find((error) => error.field === field);
  const handleBannerSelect = (banner: { url: string }) => {
    setFormData((prev: any) => ({ ...prev, bannerURL: banner.url }));
    clearError('bannerURL');
  };

  return (
    <div className="container max-w-2xl mx-auto px-4 py-8">
      <div className="space-y-6">
        <div>
          <h1 className="text-3xl font-bold">Create Your Profile</h1>
          <p className="text-muted-foreground mt-2">Tell us a bit about yourself to get started.</p>
        </div>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div className="space-y-4">
            {/* Banner Selection */}
            <div>
              <Label>Profile Banner</Label>
              <div className="grid grid-cols-2 gap-4 mt-2">
                {bannerOptions.map((banner) => (
                  <div
                    key={banner.id}
                    className={`relative aspect-video cursor-pointer rounded-lg overflow-hidden border-2 ${formData.bannerURL === banner.url ? 'border-primary' : 'border-transparent'}`}
                    onClick={() => handleBannerSelect(banner)}
                  >
                    <img src={banner.url} alt={banner.name} className="w-full h-full object-cover" />
                    {formData.bannerURL === banner.url && (
                      <div className="absolute inset-0 bg-black/20 flex items-center justify-center">
                        <Check className="h-8 w-8 text-white" />
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </div>
            {/* Display Name, Username, Bio */}
            <div>
              <Label htmlFor="displayName">Display Name</Label>
              <Input id="displayName" name="displayName" value={formData.displayName} onChange={handleChange} required placeholder="Your display name" className={cn(getFieldError('displayName')?.type === 'error' && 'border-red-500')} />
              {getFieldError('displayName') && <p className="text-red-500 text-sm mt-1">{getFieldError('displayName')?.message}</p>}
            </div>
            <div>
              <Label htmlFor="username">Username</Label>
              <Input id="username" name="username" value={formData.username} onChange={handleChange} required placeholder="Your username" className={cn(getFieldError('username')?.type === 'error' && 'border-red-500', getFieldError('username')?.type === 'success' && 'border-green-500')} />
              {getFieldError('username') && (
                <p className={cn('text-sm mt-1', getFieldError('username')?.type === 'error' && 'text-red-500', getFieldError('username')?.type === 'success' && 'text-green-500')}>{getFieldError('username')?.message}</p>
              )}
            </div>
            <div>
              <Label htmlFor="bio">Bio</Label>
              <Textarea id="bio" name="bio" value={formData.bio} onChange={handleChange} placeholder="Tell us about yourself" rows={4} />
            </div>
            {/* Country Combobox */}
            <div>
              <Label htmlFor="country">Country</Label>
              <Combobox
                options={countryList.map(c => ({ value: c.code, label: c.name, flag: c.flag }))}
                value={formData.country}
                onChange={value => {
                  setFormData(prev => ({ ...prev, country: value }));
                  clearError('country');
                }}
                placeholder="Select country..."
              />
              {getFieldError('country') && <p className="text-red-500 text-sm mt-1">{getFieldError('country')?.message}</p>}
            </div>
            {/* Role & Seniority */}
            <div>
              <Label htmlFor="role">Role</Label>
              <Select value={formData.role} onValueChange={value => { handleSelectChange('role', value); clearError('role'); }}>
                {roleOptions.map(role => (
                  <SelectItem key={role} value={role}>{role}</SelectItem>
                ))}
              </Select>
              {getFieldError('role') && <p className="text-red-500 text-sm mt-1">{getFieldError('role')?.message}</p>}
            </div>
            <div>
              <Label htmlFor="seniorityLevel">Seniority Level</Label>
              <Select value={formData.seniorityLevel} onValueChange={value => { handleSelectChange('seniorityLevel', value); clearError('seniorityLevel'); }}>
                {seniorityOptions.map(level => (
                  <SelectItem key={level} value={level}>{level}</SelectItem>
                ))}
              </Select>
              {getFieldError('seniorityLevel') && <p className="text-red-500 text-sm mt-1">{getFieldError('seniorityLevel')?.message}</p>}
            </div>
            {/* Tech Stack */}
            <div>
              <Label htmlFor="techStack">Tech Stack</Label>
              <Combobox
                options={techStackOptions.map(t => ({ value: t, label: t }))}
                value={''} // unused for multi-select
                onChange={tech => {
                  if (!formData.techStack.includes(tech) && formData.techStack.length < 15) {
                    handleTechStackChange(tech);
                    clearError('techStack');
                  } else if (formData.techStack.includes(tech)) {
                    removeTechStack(tech);
                  }
                }}
                placeholder="Select technologies..."
                className={cn(getFieldError('techStack')?.type === 'error' && 'border-red-500')}
              />
              {getFieldError('techStack') && <p className="text-red-500 text-sm mt-1">{getFieldError('techStack')?.message}</p>}
              <div className="flex flex-wrap gap-2 mt-2">
                {formData.techStack.map(tech => (
                  <Badge key={tech} variant="secondary" className="flex items-center gap-1">
                    {tech}
                    <button type="button" onClick={() => removeTechStack(tech)} className="ml-1 ring-offset-background rounded-full outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2">
                      <X className="h-3 w-3" />
                      <span className="sr-only">Remove {tech}</span>
                    </button>
                  </Badge>
                ))}
              </div>
            </div>
            {/* Website & Socials */}
            <div>
              <Label htmlFor="website">Website</Label>
              <Input id="website" name="website" value={formData.website} onChange={handleChange} placeholder="Your website" type="url" className={cn(getFieldError('website')?.type === 'error' && 'border-red-500')} />
              {getFieldError('website') && <p className="text-red-500 text-sm mt-1">{getFieldError('website')?.message}</p>}
            </div>
            <div>
              <Label htmlFor="githubUrl">GitHub</Label>
              <Input id="githubUrl" name="githubUrl" value={formData.githubUrl} onChange={handleChange} placeholder="Your GitHub username" className={cn(getFieldError('githubUrl')?.type === 'error' && 'border-red-500')} />
              {getFieldError('githubUrl') && <p className="text-red-500 text-sm mt-1">{getFieldError('githubUrl')?.message}</p>}
            </div>
            <div>
              <Label htmlFor="twitterUrl">Twitter</Label>
              <Input id="twitterUrl" name="twitterUrl" value={formData.twitterUrl} onChange={handleChange} placeholder="Your Twitter username" className={cn(getFieldError('twitterUrl')?.type === 'error' && 'border-red-500')} />
              {getFieldError('twitterUrl') && <p className="text-red-500 text-sm mt-1">{getFieldError('twitterUrl')?.message}</p>}
            </div>
            <div>
              <Label htmlFor="linkedinUrl">LinkedIn</Label>
              <Input id="linkedinUrl" name="linkedinUrl" value={formData.linkedinUrl} onChange={handleChange} placeholder="Your LinkedIn username" className={cn(getFieldError('linkedinUrl')?.type === 'error' && 'border-red-500')} />
              {getFieldError('linkedinUrl') && <p className="text-red-500 text-sm mt-1">{getFieldError('linkedinUrl')?.message}</p>}
            </div>
          </div>
          {/* Project Showcase */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <h2 className="text-xl font-semibold">Project Showcase</h2>
              <Button type="button" variant="outline" onClick={() => setShowProjectForm(!showProjectForm)} className="flex items-center gap-2">
                <Plus className="h-4 w-4" /> Add Project
              </Button>
            </div>
            {showProjectForm && (
              <div className="space-y-4 p-4 border rounded-lg">
                <div>
                  <Label htmlFor="projectTitle">Project Title</Label>
                  <Input id="projectTitle" name="title" value={currentProject.title} onChange={handleProjectChange} placeholder="Enter project title" required />
                </div>
                <div>
                  <Label htmlFor="projectDescription">Project Description</Label>
                  <Textarea id="projectDescription" name="description" value={currentProject.description} onChange={handleProjectChange} placeholder="Describe your project" rows={4} required />
                </div>
                <div>
                  <Label htmlFor="projectUrl">Project URL</Label>
                  <Input id="projectUrl" name="url" value={currentProject.url} onChange={handleProjectChange} placeholder="https://your-project.com" type="url" />
                </div>
                <div>
                  <Label htmlFor="projectImageUrl">Project Image URL</Label>
                  <Input id="projectImageUrl" name="imageUrl" value={currentProject.imageUrl} onChange={handleProjectChange} placeholder="https://postimages.org/your-project-image" type="url" />
                  <div className="mt-2 p-3 bg-blue-50 border border-blue-200 rounded-lg">
                    <div className="flex items-start gap-2">
                      <div className="flex-shrink-0 mt-0.5">
                        <svg className="w-4 h-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20"><path fillRule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7-4a1 1 0 11-2 0 1 1 0 012 0zM9 9a1 1 0 000 2v3a1 1 0 001 1h1a1 1 0 100-2v-3a1 1 0 00-1-1H9z" clipRule="evenodd" /></svg>
                      </div>
                      <div className="flex-1">
                        <p className="text-sm text-blue-800 font-medium">Need to host an image?</p>
                        <p className="text-xs text-blue-700 mt-1">We don't support direct image uploads yet. Use <a href="https://postimages.org/" target="_blank" rel="noopener noreferrer" className="underline hover:text-blue-900 font-medium">Postimages.org</a> to upload your project screenshot and paste the direct link here.</p>
                      </div>
                    </div>
                  </div>
                </div>
                <div>
                  <Label htmlFor="projectTechStack">Technologies Used</Label>
                  <Combobox
                    options={techStackOptions.map(t => ({ value: t, label: t }))}
                    value={''} // unused for multi-select
                    onChange={tech => {
                      if (!currentProject.technologies.includes(tech)) {
                        handleProjectTechStackChange(tech);
                      } else {
                        removeProjectTech(tech);
                      }
                    }}
                    placeholder="Select technologies..."
                  />
                  <div className="flex flex-wrap gap-2 mt-2">
                    {currentProject.technologies.map(tech => (
                      <Badge key={tech} variant="secondary" className="flex items-center gap-1">
                        {tech}
                        <button type="button" onClick={() => removeProjectTech(tech)} className="ml-1 ring-offset-background rounded-full outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2">
                          <X className="h-3 w-3" />
                          <span className="sr-only">Remove {tech}</span>
                        </button>
                      </Badge>
                    ))}
                  </div>
                </div>
                <div className="flex justify-end gap-2">
                  <Button type="button" variant="outline" onClick={() => setShowProjectForm(false)}>Cancel</Button>
                  <Button type="button" onClick={addProject}>Add Project</Button>
                </div>
              </div>
            )}
            {formData.projects.length > 0 && (
              <div className="space-y-4">
                {formData.projects.map(project => (
                  <div key={project.id} className="p-4 border rounded-lg space-y-2">
                    <div className="flex items-start gap-4">
                      {project.imageUrl && (
                        <div className="flex-shrink-0">
                          <img src={project.imageUrl} alt={project.title} className="w-24 h-24 object-cover rounded-lg border" onError={e => { (e.currentTarget as HTMLImageElement).style.display = 'none'; }} />
                        </div>
                      )}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between">
                          <div className="flex-1">
                            <h3 className="font-semibold">{project.title}</h3>
                            <p className="text-sm text-muted-foreground">{project.description}</p>
                            {project.url && (
                              <a href={project.url} target="_blank" rel="noopener noreferrer" className="text-sm text-blue-500 hover:underline">View Project</a>
                            )}
                          </div>
                          <Button type="button" variant="ghost" size="icon" onClick={() => removeProject(project.id)}>
                            <Trash2 className="h-4 w-4" />
                            <span className="sr-only">Remove project</span>
                          </Button>
                        </div>
                        <div className="flex flex-wrap gap-2 mt-2">
                          {project.technologies.map(tech => (
                            <Badge key={tech} variant="secondary">{tech}</Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
          <Button type="submit" className="w-full" disabled={loading || isValidating}>
            {loading ? 'Creating Profile...' : isValidating ? 'Validating...' : 'Create Profile'}
          </Button>
        </form>
      </div>
    </div>
  );
}
