import React, { useEffect, useState } from 'react';

interface PerformanceOptimizerProps {
  children: React.ReactNode;
}

// Critical performance optimizations for mobile
export const PerformanceOptimizer: React.FC<PerformanceOptimizerProps> = ({ children }) => {
  const [isOptimized, setIsOptimized] = useState(false);

  useEffect(() => {
    // Critical performance optimizations
    const optimizePerformance = () => {
      // 1. Disable smooth scrolling on mobile for better performance
      if (window.innerWidth <= 768) {
        document.documentElement.style.scrollBehavior = 'auto';
      }

      // 2. Add performance hints to the document
      const head = document.head;
      
      // Preconnect to external domains
      const preconnectDomains = [
        'https://fonts.googleapis.com',
        'https://fonts.gstatic.com',
        'https://firebaseapp.com',
        'https://firestore.googleapis.com'
      ];

      preconnectDomains.forEach(domain => {
        const link = document.createElement('link');
        link.rel = 'preconnect';
        link.href = domain;
        link.crossOrigin = 'anonymous';
        head.appendChild(link);
      });

      // 3. Add resource hints
      const resourceHints = document.createElement('link');
      resourceHints.rel = 'dns-prefetch';
      resourceHints.href = '//fonts.googleapis.com';
      head.appendChild(resourceHints);

      // 4. Optimize images loading
      const images = document.querySelectorAll('img');
      images.forEach(img => {
        if (!img.loading) {
          img.loading = 'lazy';
        }
        if (!img.decoding) {
          img.decoding = 'async';
        }
      });

      // 5. Add viewport meta tag optimization for mobile
      let viewportMeta = document.querySelector('meta[name="viewport"]') as HTMLMetaElement;
      if (!viewportMeta) {
        viewportMeta = document.createElement('meta');
        viewportMeta.name = 'viewport';
        head.appendChild(viewportMeta);
      }
      viewportMeta.content = 'width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes, viewport-fit=cover';

      // 6. Optimize for mobile performance
      if (window.innerWidth <= 768) {
        // Reduce animation duration on mobile
        const style = document.createElement('style');
        style.textContent = `
          *, *::before, *::after {
            animation-duration: 0.3s !important;
            animation-delay: 0s !important;
            transition-duration: 0.3s !important;
            transition-delay: 0s !important;
          }
          
          /* Optimize scrolling performance */
          * {
            -webkit-overflow-scrolling: touch;
            transform: translateZ(0);
          }
          
          /* Reduce motion for better performance */
          @media (prefers-reduced-motion: reduce) {
            *, *::before, *::after {
              animation-duration: 0.01ms !important;
              animation-iteration-count: 1 !important;
              transition-duration: 0.01ms !important;
            }
          }
        `;
        head.appendChild(style);
      }

      // 7. Optimize Firebase performance
      if ('serviceWorker' in navigator) {
        // Register service worker for caching (if available)
        navigator.serviceWorker.register('/sw.js').catch(() => {
          // Service worker not available, continue without it
        });
      }

      // 8. Memory management
      const cleanupMemory = () => {
        // Force garbage collection if available (Chrome DevTools)
        if (window.gc) {
          window.gc();
        }
        
        // Clear unused images from memory
        const unusedImages = document.querySelectorAll('img[data-loaded="false"]');
        unusedImages.forEach(img => {
          if (img instanceof HTMLImageElement) {
            img.src = '';
          }
        });
      };

      // Run memory cleanup every 30 seconds on mobile
      if (window.innerWidth <= 768) {
        setInterval(cleanupMemory, 30000);
      }

      // 9. Optimize critical rendering path
      const criticalCSS = `
        /* Critical CSS for above-the-fold content */
        body {
          font-display: swap;
          -webkit-font-smoothing: antialiased;
          -moz-osx-font-smoothing: grayscale;
        }
        
        /* Optimize layout shifts */
        img, video, iframe {
          max-width: 100%;
          height: auto;
        }
        
        /* Optimize repaints */
        .card, .button, .input {
          will-change: transform;
          transform: translateZ(0);
        }
      `;

      const criticalStyle = document.createElement('style');
      criticalStyle.textContent = criticalCSS;
      head.appendChild(criticalStyle);

      setIsOptimized(true);
    };

    // Run optimizations after a short delay to not block initial render
    const timeoutId = setTimeout(optimizePerformance, 100);

    return () => {
      clearTimeout(timeoutId);
    };
  }, []);

  // Show loading state while optimizations are being applied
  if (!isOptimized) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return <>{children}</>;
};

// Hook for component-level performance optimization
export const usePerformanceOptimization = (componentName: string) => {
  useEffect(() => {
    // Mark component as performance-critical
    const element = document.querySelector(`[data-component="${componentName}"]`);
    if (element) {
      (element as HTMLElement).style.willChange = 'transform';
      (element as HTMLElement).style.transform = 'translateZ(0)';
    }

    return () => {
      // Cleanup performance optimizations
      if (element) {
        (element as HTMLElement).style.willChange = 'auto';
        (element as HTMLElement).style.transform = 'none';
      }
    };
  }, [componentName]);
};

// Critical resource preloader
export const preloadCriticalResources = () => {
  const resources = [
    { href: '/placeholder-avatar.jpg', as: 'image' },
    { href: '/placeholder-cover.jpg', as: 'image' },
    // Add more critical resources as needed
  ];

  resources.forEach(resource => {
    const link = document.createElement('link');
    link.rel = 'preload';
    link.href = resource.href;
    link.as = resource.as;
    document.head.appendChild(link);
  });
};

// Mobile-specific optimizations
export const optimizeForMobile = () => {
  if (typeof window === 'undefined') return;

  const isMobile = window.innerWidth <= 768;
  
  if (isMobile) {
    // Reduce the number of concurrent network requests
    const originalFetch = window.fetch;
    let activeRequests = 0;
    const maxConcurrentRequests = 6; // Reduced for mobile

    window.fetch = async (...args) => {
      while (activeRequests >= maxConcurrentRequests) {
        await new Promise(resolve => setTimeout(resolve, 10));
      }
      
      activeRequests++;
      try {
        const response = await originalFetch(...args);
        return response;
      } finally {
        activeRequests--;
      }
    };

    // Optimize touch events
    document.addEventListener('touchstart', () => {}, { passive: true });
    document.addEventListener('touchmove', () => {}, { passive: true });
    document.addEventListener('touchend', () => {}, { passive: true });
  }
};

// Initialize performance optimizations
if (typeof window !== 'undefined') {
  // Run critical optimizations immediately
  optimizeForMobile();
  preloadCriticalResources();
}
