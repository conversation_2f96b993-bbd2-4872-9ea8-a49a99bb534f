/* Profile Photo Perfect Circle Fix */

/* Ensure all profile photos are perfectly circular */
.profile-photo-container {
  width: 96px; /* 24 * 4 = 96px (w-24) */
  height: 96px; /* 24 * 4 = 96px (h-24) */
  border-radius: 50%;
  overflow: hidden;
  background-color: white;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  position: relative;
}

.profile-photo-container img {
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  border-radius: 50% !important;
  background-color: transparent !important;
  display: block !important;
}

/* Remove any background from lazy loading containers */
.profile-photo-container .lazy-image-container {
  width: 100% !important;
  height: 100% !important;
  border-radius: 50% !important;
  overflow: hidden !important;
  background-color: transparent !important;
}

/* Ensure skeleton loaders are also circular */
.profile-photo-skeleton {
  width: 96px !important;
  height: 96px !important;
  border-radius: 50% !important;
  overflow: hidden !important;
}

/* Fix for any background bleeding */
.profile-photo-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: white;
  border-radius: 50%;
  z-index: -1;
}

/* Featured user border enhancement */
.profile-photo-featured {
  border: 4px solid #F65100 !important;
}

.profile-photo-regular {
  border: 4px solid white !important;
}

/* Crown icon uses original inline styling - no custom class needed */

/* Ensure no background shows through */
.profile-photo-container,
.profile-photo-container *,
.profile-photo-container img {
  background-image: none !important;
  background-color: transparent !important;
}

/* Override any conflicting styles */
.profile-photo-container img[style*="background"] {
  background: transparent !important;
}

/* Mobile optimization */
@media (max-width: 768px) {
  .profile-photo-container {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }
}

/* High DPI displays */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
  .profile-photo-container img {
    image-rendering: -webkit-optimize-contrast;
    image-rendering: crisp-edges;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .profile-photo-container {
    background-color: #1f2937; /* gray-800 */
  }
  
  .profile-photo-container::before {
    background-color: #1f2937; /* gray-800 */
  }
  
  .profile-photo-regular {
    border-color: #1f2937 !important;
  }
}

/* Animation for smooth loading */
.profile-photo-container img {
  transition: opacity 0.3s ease-in-out;
}

.profile-photo-loading {
  opacity: 0;
}

.profile-photo-loaded {
  opacity: 1;
}

/* Prevent layout shift */
.profile-photo-container {
  flex-shrink: 0;
  min-width: 96px;
  min-height: 96px;
}
