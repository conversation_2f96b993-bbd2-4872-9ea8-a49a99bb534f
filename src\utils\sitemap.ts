// Sitemap generation utilities for SEO

export interface SitemapUrl {
  url: string;
  lastmod?: string;
  changefreq?: 'always' | 'hourly' | 'daily' | 'weekly' | 'monthly' | 'yearly' | 'never';
  priority?: number;
}

export const generateSitemap = (urls: SitemapUrl[]): string => {
  const urlEntries = urls.map(({ url, lastmod, changefreq, priority }) => {
    let entry = `  <url>\n    <loc>${url}</loc>\n`;
    
    if (lastmod) {
      entry += `    <lastmod>${lastmod}</lastmod>\n`;
    }
    
    if (changefreq) {
      entry += `    <changefreq>${changefreq}</changefreq>\n`;
    }
    
    if (priority !== undefined) {
      entry += `    <priority>${priority.toFixed(1)}</priority>\n`;
    }
    
    entry += `  </url>`;
    return entry;
  }).join('\n');

  return `<?xml version="1.0" encoding="UTF-8"?>
<urlset xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${urlEntries}
</urlset>`;
};

export const generateSitemapIndex = (sitemaps: { url: string; lastmod?: string }[]): string => {
  const sitemapEntries = sitemaps.map(({ url, lastmod }) => {
    let entry = `  <sitemap>\n    <loc>${url}</loc>\n`;
    
    if (lastmod) {
      entry += `    <lastmod>${lastmod}</lastmod>\n`;
    }
    
    entry += `  </sitemap>`;
    return entry;
  }).join('\n');

  return `<?xml version="1.0" encoding="UTF-8"?>
<sitemapindex xmlns="http://www.sitemaps.org/schemas/sitemap/0.9">
${sitemapEntries}
</sitemapindex>`;
};

export const getStaticSitemapUrls = (baseUrl: string): SitemapUrl[] => {
  const now = new Date().toISOString();
  
  return [
    {
      url: baseUrl,
      lastmod: now,
      changefreq: 'daily',
      priority: 1.0
    },
    {
      url: `${baseUrl}/portfolios`,
      lastmod: now,
      changefreq: 'daily',
      priority: 0.9
    },
    {
      url: `${baseUrl}/pricing`,
      lastmod: now,
      changefreq: 'weekly',
      priority: 0.7
    },
    {
      url: `${baseUrl}/create-profile`,
      lastmod: now,
      changefreq: 'monthly',
      priority: 0.6
    }
  ];
};

export const getUserSitemapUrls = (users: any[], baseUrl: string): SitemapUrl[] => {
  return users.map(user => ({
    url: `${baseUrl}/${user.username}`,
    lastmod: user.updatedAt ? new Date(user.updatedAt).toISOString() : new Date().toISOString(),
    changefreq: 'weekly' as const,
    priority: user.isFeatured ? 0.8 : 0.6
  }));
};

// Generate robots.txt content
export const generateRobotsTxt = (baseUrl: string): string => {
  return `User-agent: *
Allow: /

# Disallow admin and private pages
Disallow: /admin-panel
Disallow: /settings
Disallow: /update-profile
Disallow: /payment
Disallow: /api/

# Allow important pages
Allow: /
Allow: /portfolios
Allow: /pricing
Allow: /create-profile

# Sitemap location
Sitemap: ${baseUrl}/sitemap.xml

# Crawl delay for respectful crawling
Crawl-delay: 1

# Specific rules for search engines
User-agent: Googlebot
Allow: /
Crawl-delay: 0

User-agent: Bingbot
Allow: /
Crawl-delay: 1

User-agent: Slurp
Allow: /
Crawl-delay: 2`;
};

// Generate OpenSearch description for search functionality
export const generateOpenSearchXml = (baseUrl: string, siteName: string): string => {
  return `<?xml version="1.0" encoding="UTF-8"?>
<OpenSearchDescription xmlns="http://a9.com/-/spec/opensearch/1.1/">
  <ShortName>${siteName}</ShortName>
  <Description>Search developer portfolios on ${siteName}</Description>
  <Tags>developers programming portfolio</Tags>
  <Contact>contact@${new URL(baseUrl).hostname}</Contact>
  <Url type="text/html" template="${baseUrl}/portfolios?search={searchTerms}"/>
  <Url type="application/rss+xml" template="${baseUrl}/rss.xml?q={searchTerms}"/>
  <Image height="16" width="16" type="image/x-icon">${baseUrl}/favicon.ico</Image>
  <Image height="64" width="64" type="image/png">${baseUrl}/icon-64x64.png</Image>
  <Developer>${siteName} Team</Developer>
  <Attribution>Search data provided by ${siteName}</Attribution>
  <SyndicationRight>open</SyndicationRight>
  <AdultContent>false</AdultContent>
  <Language>en-us</Language>
  <OutputEncoding>UTF-8</OutputEncoding>
  <InputEncoding>UTF-8</InputEncoding>
</OpenSearchDescription>`;
};

// Generate RSS feed for portfolios
export const generateRSSFeed = (users: any[], baseUrl: string, siteName: string): string => {
  const items = users.slice(0, 20).map(user => {
    const pubDate = user.createdAt ? new Date(user.createdAt).toUTCString() : new Date().toUTCString();
    const description = user.bio || `${user.role || 'Developer'} specializing in ${(user.techStack || []).slice(0, 3).join(', ')}`;
    
    return `    <item>
      <title>${user.displayName || user.username} - ${user.role || 'Developer'}</title>
      <link>${baseUrl}/${user.username}</link>
      <description><![CDATA[${description}]]></description>
      <pubDate>${pubDate}</pubDate>
      <guid>${baseUrl}/${user.username}</guid>
      <category>Developer Portfolio</category>
      ${user.techStack ? user.techStack.map((tech: string) => `<category>${tech}</category>`).join('\n      ') : ''}
    </item>`;
  }).join('\n');

  return `<?xml version="1.0" encoding="UTF-8"?>
<rss version="2.0" xmlns:atom="http://www.w3.org/2005/Atom">
  <channel>
    <title>${siteName} - Latest Developer Portfolios</title>
    <link>${baseUrl}</link>
    <description>Discover the latest developer portfolios and programming talent on ${siteName}</description>
    <language>en-us</language>
    <lastBuildDate>${new Date().toUTCString()}</lastBuildDate>
    <atom:link href="${baseUrl}/rss.xml" rel="self" type="application/rss+xml"/>
    <image>
      <url>${baseUrl}/logo.png</url>
      <title>${siteName}</title>
      <link>${baseUrl}</link>
    </image>
${items}
  </channel>
</rss>`;
};

// Generate JSON-LD structured data for the website
export const generateWebsiteStructuredData = (baseUrl: string, siteName: string) => {
  return {
    "@context": "https://schema.org",
    "@type": "WebSite",
    "name": siteName,
    "url": baseUrl,
    "description": "Discover talented developers and showcase your programming skills. Connect with developers worldwide and explore amazing portfolios.",
    "potentialAction": {
      "@type": "SearchAction",
      "target": {
        "@type": "EntryPoint",
        "urlTemplate": `${baseUrl}/portfolios?search={search_term_string}`
      },
      "query-input": "required name=search_term_string"
    },
    "publisher": {
      "@type": "Organization",
      "name": siteName,
      "url": baseUrl,
      "logo": {
        "@type": "ImageObject",
        "url": `${baseUrl}/logo.png`
      }
    },
    "sameAs": [
      "https://github.com/devportfolio",
      "https://twitter.com/devportfolio",
      "https://linkedin.com/company/devportfolio"
    ]
  };
};

// Generate breadcrumb structured data
export const generateBreadcrumbStructuredData = (breadcrumbs: { name: string; url: string }[]) => {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": breadcrumbs.map((crumb, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": crumb.name,
      "item": crumb.url
    }))
  };
};

// Utility to create sitemap files
export const createSitemapFiles = async (users: any[], baseUrl: string) => {
  const staticUrls = getStaticSitemapUrls(baseUrl);
  const userUrls = getUserSitemapUrls(users, baseUrl);
  
  // Main sitemap with static pages
  const mainSitemap = generateSitemap(staticUrls);
  
  // User profiles sitemap (split if too many users)
  const maxUrlsPerSitemap = 50000; // Google's limit
  const userSitemaps: string[] = [];
  
  for (let i = 0; i < userUrls.length; i += maxUrlsPerSitemap) {
    const chunk = userUrls.slice(i, i + maxUrlsPerSitemap);
    userSitemaps.push(generateSitemap(chunk));
  }
  
  // Sitemap index if multiple sitemaps
  let sitemapIndex = '';
  if (userSitemaps.length > 1) {
    const sitemapIndexUrls = [
      { url: `${baseUrl}/sitemap-main.xml`, lastmod: new Date().toISOString() },
      ...userSitemaps.map((_, index) => ({
        url: `${baseUrl}/sitemap-users-${index + 1}.xml`,
        lastmod: new Date().toISOString()
      }))
    ];
    sitemapIndex = generateSitemapIndex(sitemapIndexUrls);
  }
  
  return {
    mainSitemap,
    userSitemaps,
    sitemapIndex,
    rss: generateRSSFeed(users, baseUrl, 'DevPortfolio'),
    opensearch: generateOpenSearchXml(baseUrl, 'DevPortfolio')
  };
};
