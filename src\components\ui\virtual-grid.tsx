import React, { useState, useEffect, useRef, useCallback, useMemo } from 'react';
import { cn } from '@/lib/utils';

interface VirtualGridProps<T> {
  items: T[];
  itemHeight: number;
  itemWidth: number;
  containerHeight: number;
  gap?: number;
  className?: string;
  renderItem: (item: T, index: number) => React.ReactNode;
  onLoadMore?: () => void;
  hasMore?: boolean;
  isLoading?: boolean;
  loadMoreThreshold?: number;
}

export function VirtualGrid<T>({
  items,
  itemHeight,
  itemWidth,
  containerHeight,
  gap = 8,
  className,
  renderItem,
  onLoadMore,
  hasMore = false,
  isLoading = false,
  loadMoreThreshold = 5
}: VirtualGridProps<T>) {
  const [scrollTop, setScrollTop] = useState(0);
  const [containerWidth, setContainerWidth] = useState(0);
  const containerRef = useRef<HTMLDivElement>(null);
  const scrollElementRef = useRef<HTMLDivElement>(null);

  // Calculate columns based on container width
  const columnsCount = useMemo(() => {
    if (containerWidth === 0) return 1;
    return Math.floor((containerWidth + gap) / (itemWidth + gap));
  }, [containerWidth, itemWidth, gap]);

  // Calculate total rows
  const rowsCount = useMemo(() => {
    return Math.ceil(items.length / columnsCount);
  }, [items.length, columnsCount]);

  // Calculate visible range
  const visibleRange = useMemo(() => {
    const startRow = Math.floor(scrollTop / (itemHeight + gap));
    const endRow = Math.min(
      rowsCount - 1,
      Math.ceil((scrollTop + containerHeight) / (itemHeight + gap))
    );

    const startIndex = startRow * columnsCount;
    const endIndex = Math.min(items.length - 1, (endRow + 1) * columnsCount - 1);

    return {
      startRow: Math.max(0, startRow),
      endRow: Math.max(0, endRow),
      startIndex: Math.max(0, startIndex),
      endIndex: Math.max(0, endIndex)
    };
  }, [scrollTop, containerHeight, itemHeight, gap, rowsCount, columnsCount, items.length]);

  // Get visible items
  const visibleItems = useMemo(() => {
    return items.slice(visibleRange.startIndex, visibleRange.endIndex + 1);
  }, [items, visibleRange.startIndex, visibleRange.endIndex]);

  // Handle scroll
  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    const scrollTop = e.currentTarget.scrollTop;
    setScrollTop(scrollTop);

    // Load more when near bottom
    if (onLoadMore && hasMore && !isLoading) {
      const scrollHeight = e.currentTarget.scrollHeight;
      const clientHeight = e.currentTarget.clientHeight;
      const threshold = (itemHeight + gap) * loadMoreThreshold;
      
      if (scrollTop + clientHeight >= scrollHeight - threshold) {
        onLoadMore();
      }
    }
  }, [onLoadMore, hasMore, isLoading, itemHeight, gap, loadMoreThreshold]);

  // Handle resize
  useEffect(() => {
    const handleResize = () => {
      if (containerRef.current) {
        setContainerWidth(containerRef.current.clientWidth);
      }
    };

    handleResize();
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);

  // Calculate total height
  const totalHeight = rowsCount * (itemHeight + gap) - gap;

  // Calculate offset for visible items
  const offsetY = visibleRange.startRow * (itemHeight + gap);

  return (
    <div
      ref={containerRef}
      className={cn('relative overflow-hidden', className)}
      style={{ height: containerHeight }}
    >
      <div
        ref={scrollElementRef}
        className="overflow-auto h-full w-full"
        onScroll={handleScroll}
      >
        <div
          className="relative"
          style={{ height: totalHeight }}
        >
          <div
            className="absolute top-0 left-0 w-full"
            style={{
              transform: `translateY(${offsetY}px)`,
              display: 'grid',
              gridTemplateColumns: `repeat(${columnsCount}, ${itemWidth}px)`,
              gap: `${gap}px`,
              justifyContent: 'center'
            }}
          >
            {visibleItems.map((item, index) => {
              const actualIndex = visibleRange.startIndex + index;
              return (
                <div
                  key={actualIndex}
                  style={{
                    width: itemWidth,
                    height: itemHeight
                  }}
                >
                  {renderItem(item, actualIndex)}
                </div>
              );
            })}
          </div>
        </div>
      </div>
    </div>
  );
}

// Hook for managing virtual grid state
export const useVirtualGrid = <T,>(
  items: T[],
  itemHeight: number,
  itemWidth: number,
  containerHeight: number
) => {
  const [scrollTop, setScrollTop] = useState(0);
  const [containerWidth, setContainerWidth] = useState(0);

  const columnsCount = useMemo(() => {
    if (containerWidth === 0) return 1;
    return Math.floor(containerWidth / itemWidth);
  }, [containerWidth, itemWidth]);

  const rowsCount = useMemo(() => {
    return Math.ceil(items.length / columnsCount);
  }, [items.length, columnsCount]);

  const visibleRange = useMemo(() => {
    const startRow = Math.floor(scrollTop / itemHeight);
    const endRow = Math.min(
      rowsCount - 1,
      Math.ceil((scrollTop + containerHeight) / itemHeight)
    );

    return {
      startRow: Math.max(0, startRow),
      endRow: Math.max(0, endRow),
      startIndex: Math.max(0, startRow * columnsCount),
      endIndex: Math.min(items.length - 1, (endRow + 1) * columnsCount - 1)
    };
  }, [scrollTop, containerHeight, itemHeight, rowsCount, columnsCount, items.length]);

  return {
    scrollTop,
    setScrollTop,
    containerWidth,
    setContainerWidth,
    columnsCount,
    rowsCount,
    visibleRange
  };
};
