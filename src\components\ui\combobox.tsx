import * as React from 'react';
import { ChevronsUpDown, Check } from 'lucide-react';
import { cn } from '@/lib/utils';

interface ComboboxOption {
  value: string;
  label: string;
  flag?: string; // Optional flag image URL
}

interface ComboboxProps {
  options: ComboboxOption[];
  value?: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
}

export const Combobox: React.FC<ComboboxProps> = ({
  options,
  value,
  onChange,
  placeholder = 'Select...',
  className = '',
}) => {
  const [open, setOpen] = React.useState(false);
  const [search, setSearch] = React.useState('');
  const inputRef = React.useRef<HTMLInputElement>(null);
  const listRef = React.useRef<HTMLDivElement>(null);

  const filtered = options.filter(opt =>
    opt.label.toLowerCase().includes(search.toLowerCase())
  );

  const selected = options.find(opt => opt.value === value);

  React.useEffect(() => {
    if (open && inputRef.current) {
      inputRef.current.focus();
    }
  }, [open]);

  // Keyboard navigation
  React.useEffect(() => {
    if (!open) return;
    const handler = (e: KeyboardEvent) => {
      if (e.key === 'Escape') setOpen(false);
    };
    window.addEventListener('keydown', handler);
    return () => window.removeEventListener('keydown', handler);
  }, [open]);

  return (
    <div className={cn('relative', className)}>
      <button
        type="button"
        className={cn(
          'flex w-full items-center justify-between rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2',
          'disabled:cursor-not-allowed disabled:opacity-50',
          'cursor-pointer',
          open && 'ring-2 ring-ring',
        )}
        onClick={() => setOpen(o => !o)}
        aria-haspopup="listbox"
        aria-expanded={open}
      >
        <span className={selected ? 'text-foreground flex items-center gap-2' : 'text-muted-foreground'}>
          {selected && selected.flag && (
            <img src={selected.flag} alt="" className="w-5 h-5 rounded-full" />
          )}
          {selected ? selected.label : placeholder}
        </span>
        <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
      </button>
      {open && (
        <div
          ref={listRef}
          className="absolute z-50 mt-1 w-full max-h-60 overflow-auto rounded-md border border-input bg-background shadow-lg"
          tabIndex={-1}
          role="listbox"
        >
          <div className="p-2">
            <input
              ref={inputRef}
              type="text"
              value={search}
              onChange={e => setSearch(e.target.value)}
              placeholder="Search..."
              className="w-full px-2 py-1 border rounded mb-2 text-sm"
              onKeyDown={e => {
                if (e.key === 'ArrowDown') {
                  const first = listRef.current?.querySelector('[data-combobox-option]');
                  (first as HTMLElement)?.focus();
                }
              }}
            />
          </div>
          {filtered.length === 0 && (
            <div className="px-3 py-2 text-sm text-muted-foreground">No options found.</div>
          )}
          {filtered.map(opt => (
            <button
              key={opt.value}
              type="button"
              data-combobox-option
              className={cn(
                'flex w-full items-center gap-2 px-3 py-2 text-sm cursor-pointer hover:bg-accent hover:text-accent-foreground',
                value === opt.value && 'bg-accent text-accent-foreground',
              )}
              onClick={() => {
                onChange(opt.value);
                setOpen(false);
                setSearch('');
              }}
            >
              {opt.flag && <img src={opt.flag} alt="" className="w-5 h-5 rounded-full" />}
              <span className="flex-1 text-left">{opt.label}</span>
              {value === opt.value && <Check className="h-4 w-4" />}
            </button>
          ))}
        </div>
      )}
    </div>
  );
}; 