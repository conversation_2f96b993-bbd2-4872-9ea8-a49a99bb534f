import { useEffect, type ReactNode } from 'react';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '@/contexts/AuthContext';

interface ProtectedRouteProps {
  children: ReactNode;
  requireAuth?: boolean;
  requireProfile?: boolean;
  requireAdmin?: boolean;
  redirectTo?: string;
}

export default function ProtectedRoute({ 
  children, 
  requireAuth = false, 
  requireProfile = false,
  requireAdmin = false,
  redirectTo = '/'
}: ProtectedRouteProps) {
  const { user, userProfile, loading } = useAuth();
  const navigate = useNavigate();

  useEffect(() => {
    if (loading) return; // Wait for auth state to load

    // If authentication is required but user is not authenticated
    if (requireAuth && !user) {
      navigate(redirectTo);
      return;
    }

    // If profile is required but user doesn't have a profile
    if (requireProfile && user && !user.hasProfile) {
      navigate('/create-profile');
      return;
    }

    // If admin is required but user is not admin
    if (requireAdmin && (!userProfile || !userProfile.isAdmin)) {
      navigate('/');
      return;
    }

    // If user has profile but is trying to access create-profile
    if (user?.hasProfile && window.location.pathname === '/create-profile') {
      // Redirect to their specific profile URL
      if (userProfile?.username) {
        navigate(`/${userProfile.username}`);
      } else {
        navigate('/');
      }
      return;
    }
  }, [user, userProfile, loading, requireAuth, requireProfile, requireAdmin, navigate, redirectTo]);

  // Show loading while checking auth state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-primary"></div>
      </div>
    );
  }

  // If authentication is required but user is not authenticated, don't render children
  if (requireAuth && !user) {
    return null;
  }

  // If profile is required but user doesn't have a profile, don't render children
  if (requireProfile && user && !user.hasProfile) {
    return null;
  }

  // If admin is required but user is not admin, don't render children
  if (requireAdmin && (!userProfile || !userProfile.isAdmin)) {
    return null;
  }

  return <>{children}</>;
}
