import { DotLottieReact } from "@lottiefiles/dotlottie-react";
import { <PERSON> } from "react-router-dom";
import { But<PERSON> } from "@/components/ui/button";
import { MagicCard } from "@/components/magicui/magic-card";

const NotFound = () => {
  return (
    <div className="flex flex-col items-center justify-center h-full bg-background text-foreground">
      <MagicCard
        className="w-full max-w-md p-8 text-center rounded-2xl"
        gradientFrom="#7C3AED"
        gradientTo="#DB2777"
        gradientSize={300}
      >
        <div className="relative mx-auto h-64 w-64">
          <DotLottieReact
            src="/notFound/Animation - 1750448974926.json"
            loop
            autoplay
          />
        </div>
        <h1 className="mt-4 text-5xl font-bold tracking-tight text-gray-900 dark:text-gray-50 sm:text-6xl">
          404
        </h1>
        <p className="mt-4 text-lg text-gray-600 dark:text-gray-400">
          Oops! Page not found.
        </p>
        <p className="mt-2 text-base text-gray-500 dark:text-gray-500">
          The page you are looking for does not exist or has been moved.
        </p>
        <Button asChild className="mt-8">
          <Link to="/">Go back home</Link>
        </Button>
      </MagicCard>
    </div>
  );
};

export default NotFound;