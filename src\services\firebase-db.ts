import {
  collection,
  doc,
  getDoc,
  getDocs,
  query,
  where,
  orderBy,
  limit,
  startAfter,
  setDoc,
  deleteDoc,
  select
} from 'firebase/firestore';
import { db } from '@/config/firebase';

// User Profile interface matching the existing User type
export interface UserProfile {
  uid: string;
  email: string | null;
  displayName: string;
  username: string;
  bio: string;
  country: string;
  role: string;
  seniorityLevel: string;
  website: string;
  githubUrl: string;
  twitterUrl: string;
  linkedinUrl: string;
  techStack: string[];
  projects?: Project[];
  bannerURL: string;
  photoURL: string | null;
  createdAt: number;
  updatedAt: number;
  isVerified: boolean;
  isFeatured: boolean;
  isAdmin: boolean;
  // Additional fields that might be in the profile
  joinDate?: string;
  stats?: {
    projects?: number;
    followers?: number;
    following?: number;
    contributions?: number;
    [key: string]: number | undefined;
  };
  availableForWork?: boolean;
  workType?: string;
  hourlyRate?: string;
  resumeUrl?: string;
  ascript?: string;
}

export interface Project {
  id: string;
  title: string;
  description: string;
  url: string;
  imageUrl: string;
  technologies: string[];
}

// Function to get user by username
export const getUserByUsername = async (username: string): Promise<UserProfile | null> => {
  try {
    const usersRef = collection(db, 'users');
    const q = query(usersRef, where('username', '==', username));
    const querySnapshot = await getDocs(q);
    
    if (querySnapshot.empty) {
      return null;
    }
    
    const userDoc = querySnapshot.docs[0];
    return {
      uid: userDoc.id,
      ...userDoc.data()
    } as UserProfile;
  } catch (error) {
    console.error('Error fetching user by username:', error);
    return null;
  }
};

// Function to get user by UID
export const getUserByUid = async (uid: string): Promise<UserProfile | null> => {
  try {
    const userDoc = await getDoc(doc(db, 'users', uid));

    if (!userDoc.exists()) {
      return null;
    }

    const userData = {
      uid: userDoc.id,
      ...userDoc.data()
    } as UserProfile;

    return userData;
  } catch (error) {
    console.error('Error fetching user by UID:', error);
    return null;
  }
};

// Function to get user by email
export const getUserByEmail = async (email: string): Promise<UserProfile | null> => {
  try {
    const usersRef = collection(db, 'users');
    const q = query(usersRef, where('email', '==', email));
    const querySnapshot = await getDocs(q);
    
    if (querySnapshot.empty) {
      return null;
    }
    
    const userDoc = querySnapshot.docs[0];
    return {
      uid: userDoc.id,
      ...userDoc.data()
    } as UserProfile;
  } catch (error) {
    console.error('Error fetching user by email:', error);
    return null;
  }
};

// Function to get all users with pagination (optimized with field selection)
export const getUsers = async (
  limitCount: number = 10,
  lastDoc?: any
): Promise<{ users: UserProfile[]; lastDoc: any }> => {
  try {
    let q = query(
      collection(db, 'users'),
      orderBy('createdAt', 'desc'),
      limit(limitCount)
    );

    if (lastDoc) {
      q = query(q, startAfter(lastDoc));
    }

    const querySnapshot = await getDocs(q);
    const users: UserProfile[] = querySnapshot.docs.map(doc => ({
      uid: doc.id,
      ...doc.data()
    } as UserProfile));

    // Shuffle users randomly
    users.sort(() => Math.random() - 0.5);

    const lastVisible = querySnapshot.docs[querySnapshot.docs.length - 1] || null;

    return { users, lastDoc: lastVisible };
  } catch (error) {
    console.error('Error fetching users:', error);
    return { users: [], lastDoc: null };
  }
};

// Optimized function to get users for listing (only essential fields)
export const getUsersOptimized = async (
  limitCount: number = 10,
  lastDoc?: any
): Promise<{ users: Partial<UserProfile>[]; lastDoc: any }> => {
  try {
    let q = query(
      collection(db, 'users'),
      orderBy('createdAt', 'desc'),
      limit(limitCount)
    );

    if (lastDoc) {
      q = query(q, startAfter(lastDoc));
    }

    const querySnapshot = await getDocs(q);

    // Only select essential fields for listing to reduce data transfer
    const users: Partial<UserProfile>[] = querySnapshot.docs.map(doc => {
      const data = doc.data();
      return {
        uid: doc.id,
        displayName: data.displayName,
        username: data.username,
        photoURL: data.photoURL,
        bannerURL: data.bannerURL,
        bio: data.bio,
        role: data.role,
        country: data.country,
        techStack: data.techStack,
        isFeatured: data.isFeatured,
        isVerified: data.isVerified,
        availableForWork: data.availableForWork,
        seniorityLevel: data.seniorityLevel,
        githubUrl: data.githubUrl,
        linkedinUrl: data.linkedinUrl,
        createdAt: data.createdAt,
        updatedAt: data.updatedAt
      };
    });

    // Shuffle users randomly
    users.sort(() => Math.random() - 0.5);

    const lastVisible = querySnapshot.docs[querySnapshot.docs.length - 1] || null;

    return { users, lastDoc: lastVisible };
  } catch (error) {
    console.error('Error fetching users optimized:', error);
    return { users: [], lastDoc: null };
  }
};

// Function to get featured users
export const getFeaturedUsers = async (): Promise<UserProfile[]> => {
  try {
    const q = query(
      collection(db, 'users'),
      where('isFeatured', '==', true),
      orderBy('displayName')
    );
    
    const querySnapshot = await getDocs(q);
    return querySnapshot.docs.map(doc => ({
      uid: doc.id,
      ...doc.data()
    } as UserProfile));
  } catch (error) {
    console.error('Error fetching featured users:', error);
    return [];
  }
};

// Function to check if user exists by email
export const checkUserExistsByEmail = async (email: string): Promise<boolean> => {
  try {
    const usersRef = collection(db, 'users');
    const q = query(usersRef, where('email', '==', email));
    const querySnapshot = await getDocs(q);
    return !querySnapshot.empty;
  } catch (error) {
    console.error('Error checking user by email:', error);
    return false;
  }
};

// Function to check if user exists by UID
export const checkUserExistsByUid = async (uid: string): Promise<boolean> => {
  try {
    const userDoc = await getDoc(doc(db, 'users', uid));
    return userDoc.exists();
  } catch (error) {
    console.error('Error checking user by UID:', error);
    return false;
  }
};

// Function to check if username exists
export const checkUsernameExists = async (username: string): Promise<boolean> => {
  try {
    const usersRef = collection(db, 'users');
    const q = query(usersRef, where('username', '==', username));
    const querySnapshot = await getDocs(q);
    return !querySnapshot.empty;
  } catch (error) {
    console.error('Error checking username:', error);
    return false;
  }
};

// Function to get user by email and update their UID
export const getUserByEmailAndUpdateUid = async (email: string, newUid: string): Promise<boolean> => {
  try {
    const usersRef = collection(db, 'users');
    const q = query(usersRef, where('email', '==', email));
    const querySnapshot = await getDocs(q);
    
    if (!querySnapshot.empty) {
      const userDoc = querySnapshot.docs[0];
      const userData = userDoc.data();
      
      // Update the user document with the new UID
      await setDoc(doc(db, 'users', newUid), {
        ...userData,
        uid: newUid,
        updatedAt: Date.now()
      });
      
      // Delete the old document
      await deleteDoc(doc(db, 'users', userDoc.id));
      
      return true;
    }
    return false;
  } catch (error) {
    console.error('Error updating user UID:', error);
    return false;
  }
};

// Update user profile by UID
export const updateUser = async (uid: string, data: Partial<UserProfile>): Promise<boolean> => {
  try {
    await setDoc(doc(db, 'users', uid), {
      ...data,
      updatedAt: Date.now(),
    }, { merge: true });
    return true;
  } catch (error) {
    console.error('Error updating user:', error);
    return false;
  }
};

// Delete user profile by UID
export const deleteUser = async (uid: string): Promise<boolean> => {
  try {
    await deleteDoc(doc(db, 'users', uid));
    return true;
  } catch (error) {
    console.error('Error deleting user:', error);
    return false;
  }
};

// Search users by display name (client-side filtering)
export const searchUserByName = async (name: string, allUsers: UserProfile[]): Promise<UserProfile[]> => {
  try {
    if (!name.trim()) {
      return [];
    }

    const searchTerm = name.toLowerCase();
    
    // Filter users whose displayName starts with the search term (case-insensitive)
    const results = allUsers.filter(user => 
      user.displayName?.toLowerCase().startsWith(searchTerm)
    );

    return results;
  } catch (error) {
    console.error('Error searching users by name:', error);
    return [];
  }
}; 