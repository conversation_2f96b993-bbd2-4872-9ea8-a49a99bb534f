export interface User {
  uid: string;
  email: string | null;
  displayName: string;
  username: string;
  bio: string;
  country: string;
  role: string;
  seniorityLevel: string;
  website: string;
  githubUrl: string;
  twitterUrl: string;
  linkedinUrl: string;
  techStack: string[];
  projects: Project[];
  bannerURL: string;
  photoURL: string | null;
  createdAt: number;
  updatedAt: number;
  isVerified: boolean;
  isFeatured: boolean;
  isAdmin: boolean;
}

export interface Project {
  id: string;
  title: string;
  description: string;
  url: string;
  imageUrl: string;
  technologies: string[];
} 