import { useState, useCallback } from "react";
import type { User } from "@/types/user";

interface ValidationError {
  field: string;
  message: string;
  type: "error" | "success";
}

interface FormValidationHook<T> {
  formData: T;
  setFormData: React.Dispatch<React.SetStateAction<T>>;
  isValidating: boolean;
  handleChange: (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => void;
  handleSelectChange: (field: keyof T, value: string) => void;
  handleTechStackChange: (value: string) => void;
  validateAll: () => Promise<boolean>;
  clearError: (field: string) => void;
}

export function useFormValidation<T>(
  initialData: T,
  existingUsers: User[]
): FormValidationHook<T> & { errors: ValidationError[], setErrors: React.Dispatch<React.SetStateAction<ValidationError[]>> } {
  const [formData, setFormData] = useState<T>(initialData);
  const [isValidating, setIsValidating] = useState(false);
  const [errors, setErrors] = useState<ValidationError[]>([]);

  const handleChange = useCallback(
    (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
      const { name, value } = e.target;
      setFormData((prev) => ({ ...prev, [name]: value }));
    },
    []
  );

  const handleSelectChange = useCallback(
    (field: keyof T, value: string) => {
      setFormData((prev) => ({ ...prev, [field]: value }));
    },
    []
  );

  const handleTechStackChange = useCallback((value: string) => {
    setFormData((prev: any) => {
      const currentTechStack = prev.techStack || [];
      if (currentTechStack.includes(value)) {
        return {
          ...prev,
          techStack: currentTechStack.filter((tech: string) => tech !== value),
        };
      } else {
        return {
          ...prev,
          techStack: [...currentTechStack, value],
        };
      }
    });
  }, []);

  const validateField = useCallback(
    (field: string, value: any): ValidationError | null => {
      switch (field) {
        case "displayName":
          if (!value || value.trim().length < 2) {
            return {
              field,
              message: "Display name must be at least 2 characters long",
              type: "error",
            };
          }
          if (value.trim().length > 50) {
            return {
              field,
              message: "Display name must be less than 50 characters",
              type: "error",
            };
          }
          break;

        case "username":
          if (!value || value.trim().length < 3) {
            return {
              field,
              message: "Username must be at least 3 characters long",
              type: "error",
            };
          }
          if (value.trim().length > 30) {
            return {
              field,
              message: "Username must be less than 30 characters",
              type: "error",
            };
          }
          if (!/^[a-zA-Z0-9_-]+$/.test(value)) {
            return {
              field,
              message: "Username can only contain letters, numbers, hyphens, and underscores",
              type: "error",
            };
          }
          // Check if username is already taken
          const isUsernameTaken = existingUsers.some(
            (user) => user.username?.toLowerCase() === value.toLowerCase()
          );
          if (isUsernameTaken) {
            return {
              field,
              message: "This username is already taken",
              type: "error",
            };
          }
          break;

        case "bio":
          if (!value || value.trim().length < 10) {
            return {
              field,
              message: "Bio must be at least 10 characters long",
              type: "error",
            };
          }
          if (value.trim().length > 500) {
            return {
              field,
              message: "Bio must be less than 500 characters",
              type: "error",
            };
          }
          break;

        case "country":
          if (!value) {
            return {
              field,
              message: "Please select your country",
              type: "error",
            };
          }
          break;

        case "role":
          if (!value) {
            return {
              field,
              message: "Please select your role",
              type: "error",
            };
          }
          break;

        case "seniorityLevel":
          if (!value) {
            return {
              field,
              message: "Please select your seniority level",
              type: "error",
            };
          }
          break;

        case "website":
          if (value && !isValidUrl(value)) {
            return {
              field,
              message: "Please enter a valid website URL",
              type: "error",
            };
          }
          break;

        case "githubUrl":
          if (value && !/^[a-zA-Z0-9-]+$/.test(value)) {
            return {
              field,
              message: "Please enter a valid GitHub username",
              type: "error",
            };
          }
          break;

        case "twitterUrl":
          if (value && !/^[a-zA-Z0-9_]+$/.test(value)) {
            return {
              field,
              message: "Please enter a valid Twitter username",
              type: "error",
            };
          }
          break;

        case "linkedinUrl":
          if (value && !/^[a-zA-Z0-9-]+$/.test(value)) {
            return {
              field,
              message: "Please enter a valid LinkedIn username",
              type: "error",
            };
          }
          break;

        case "techStack":
          if (!value || (Array.isArray(value) && value.length === 0)) {
            return {
              field,
              message: "Please select at least one technology",
              type: "error",
            };
          }
          if (Array.isArray(value) && value.length > 15) {
            return {
              field,
              message: "You can select up to 15 technologies",
              type: "error",
            };
          }
          break;

        default:
          break;
      }

      return null;
    },
    [existingUsers]
  );

  const isValidUrl = (url: string): boolean => {
    try {
      new URL(url.startsWith("http") ? url : `https://${url}`);
      return true;
    } catch {
      return false;
    }
  };

  const validateAll = useCallback(async (): Promise<boolean> => {
    setIsValidating(true);
    try {
      const validationErrors: ValidationError[] = [];
      const data = formData as any;
      const fieldsToValidate = [
        "displayName",
        "username",
        "bio",
        "country",
        "role",
        "seniorityLevel",
        "techStack",
      ];
      for (const field of fieldsToValidate) {
        const error = validateField(field, data[field]);
        if (error) {
          validationErrors.push(error);
        }
      }
      const optionalFields = ["website", "githubUrl", "twitterUrl", "linkedinUrl"];
      for (const field of optionalFields) {
        if (data[field]) {
          const error = validateField(field, data[field]);
          if (error) {
            validationErrors.push(error);
          }
        }
      }
      setErrors(validationErrors);
      return validationErrors.length === 0;
    } finally {
      setIsValidating(false);
    }
  }, [formData, validateField]);

  const clearError = useCallback((field: string) => {
    setErrors(prev => prev.filter(err => err.field !== field));
  }, []);

  return {
    formData,
    setFormData,
    isValidating,
    handleChange,
    handleSelectChange,
    handleTechStackChange,
    validateAll,
    clearError,
    errors,
    setErrors,
  };
} 