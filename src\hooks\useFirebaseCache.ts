import { useState, useEffect, useCallback } from 'react';
import { getUserByUsername, getUsers, type UserProfile } from '@/services/firebase-db';

interface CacheData<T = any> {
  data: T;
  timestamp: number;
  error: string | null;
  version: number;
}

interface UseUserByUsernameReturn {
  data: UserProfile | null;
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

interface UseAllUsersReturn {
  data: UserProfile[];
  isLoading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
}

interface UseInvalidateCacheReturn {
  mutateAsync: () => Promise<void>;
}

interface UsePaginatedUsersReturn {
  data: UserProfile[];
  isLoading: boolean;
  isLoadingMore: boolean;
  error: string | null;
  hasMore: boolean;
  loadMore: () => Promise<void>;
  refetch: () => Promise<void>;
}

// Enhanced cache manager with persistent storage and mobile-specific optimization
class CacheManager {
  private memoryCache = new Map<string, CacheData>();
  private readonly CACHE_VERSION = 1;
  private readonly CACHE_DURATION = 30 * 60 * 1000; // 30 minutes for ultra-fast performance
  private readonly USERS_CACHE_DURATION = 60 * 60 * 1000; // 1 hour for all users
  private readonly MAX_CACHE_SIZE = this.isMobile() ? 100 : 200; // Reduce cache size on mobile
  private readonly STORAGE_PREFIX = 'firebase_cache_v1_';
  private backgroundRefreshTimers = new Map<string, NodeJS.Timeout>();

  // Detect mobile device for optimized caching
  private isMobile(): boolean {
    return typeof window !== 'undefined' &&
           (window.innerWidth <= 768 || /Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent));
  }

  // Get cache key with version
  private getCacheKey(key: string): string {
    return `${this.STORAGE_PREFIX}${key}`;
  }

  // Load from persistent storage
  private loadFromStorage(key: string): CacheData | null {
    try {
      const storageKey = this.getCacheKey(key);
      const stored = localStorage.getItem(storageKey);
      if (!stored) return null;

      const data: CacheData = JSON.parse(stored);

      // Check version compatibility
      if (data.version !== this.CACHE_VERSION) {
        localStorage.removeItem(storageKey);
        return null;
      }

      return data;
    } catch (error) {
      return null;
    }
  }

  // Save to persistent storage
  private saveToStorage(key: string, data: CacheData): void {
    try {
      const storageKey = this.getCacheKey(key);
      localStorage.setItem(storageKey, JSON.stringify(data));
    } catch (error) {
      // Storage full or unavailable, continue with memory cache only
    }
  }

  // Get data from cache (memory first, then storage)
  get<T>(key: string): CacheData<T> | null {
    // Check memory cache first
    let cached = this.memoryCache.get(key) as CacheData<T>;

    // If not in memory, check persistent storage
    if (!cached) {
      cached = this.loadFromStorage(key) as CacheData<T>;
      if (cached) {
        // Load back into memory cache
        this.memoryCache.set(key, cached);
      }
    }

    if (!cached) return null;

    const now = Date.now();
    const maxAge = key === 'all-users' ? this.USERS_CACHE_DURATION : this.CACHE_DURATION;

    // Check if cache is still valid
    if (now - cached.timestamp > maxAge) {
      this.delete(key);
      return null;
    }

    return cached;
  }

  // Set data in cache (both memory and storage) with background refresh
  set<T>(key: string, data: T, error: string | null = null): void {
    const cacheData: CacheData<T> = {
      data,
      timestamp: Date.now(),
      error,
      version: this.CACHE_VERSION
    };

    // Store in memory
    this.memoryCache.set(key, cacheData);

    // Store persistently (skip on mobile if low storage)
    if (!this.isMobile() || this.hasStorageSpace()) {
      this.saveToStorage(key, cacheData);
    }

    // Setup background refresh for important data
    this.setupBackgroundRefresh(key);

    // Cleanup if needed
    this.cleanup();
  }

  // Check if device has enough storage space
  private hasStorageSpace(): boolean {
    try {
      const testKey = 'storage_test';
      localStorage.setItem(testKey, 'test');
      localStorage.removeItem(testKey);
      return true;
    } catch {
      return false;
    }
  }

  // Setup background refresh for stale data
  private setupBackgroundRefresh(key: string): void {
    // Clear existing timer
    const existingTimer = this.backgroundRefreshTimers.get(key);
    if (existingTimer) {
      clearTimeout(existingTimer);
    }

    // Set new timer for background refresh
    const refreshTime = key === 'all-users' ? this.USERS_CACHE_DURATION * 0.8 : this.CACHE_DURATION * 0.8;
    const timer = setTimeout(() => {
      this.markForRefresh(key);
    }, refreshTime);

    this.backgroundRefreshTimers.set(key, timer);
  }

  // Mark data for background refresh
  private markForRefresh(key: string): void {
    const cached = this.memoryCache.get(key);
    if (cached) {
      // Add a flag to indicate this data should be refreshed in background
      (cached as any).needsRefresh = true;
    }
  }

  // Delete from cache
  delete(key: string): void {
    this.memoryCache.delete(key);
    try {
      localStorage.removeItem(this.getCacheKey(key));
    } catch (error) {
      // Ignore storage errors
    }
  }

  // Clear all cache
  clear(): void {
    this.memoryCache.clear();

    // Clear persistent storage
    try {
      const keys = Object.keys(localStorage);
      keys.forEach(key => {
        if (key.startsWith(this.STORAGE_PREFIX)) {
          localStorage.removeItem(key);
        }
      });
    } catch (error) {
      // Ignore storage errors
    }
  }

  // Cleanup old entries
  private cleanup(): void {
    if (this.memoryCache.size <= this.MAX_CACHE_SIZE) return;

    const entries = Array.from(this.memoryCache.entries());
    entries.sort((a, b) => a[1].timestamp - b[1].timestamp);

    const entriesToRemove = entries.slice(0, this.memoryCache.size - this.MAX_CACHE_SIZE);
    entriesToRemove.forEach(([key]) => this.delete(key));
  }

  // Get cache statistics
  getStats() {
    const now = Date.now();
    const stats = {
      totalEntries: this.memoryCache.size,
      maxSize: this.MAX_CACHE_SIZE,
      entries: [] as Array<{
        key: string;
        timestamp: number;
        age: number;
        hasData: boolean;
        hasError: boolean;
        isExpired: boolean;
      }>
    };

    for (const [key, value] of this.memoryCache.entries()) {
      const age = now - value.timestamp;
      const maxAge = key === 'all-users' ? this.USERS_CACHE_DURATION : this.CACHE_DURATION;
      const isExpired = age > maxAge;

      stats.entries.push({
        key,
        timestamp: value.timestamp,
        age,
        hasData: !!value.data,
        hasError: !!value.error,
        isExpired
      });
    }

    return stats;
  }
}

// Global cache manager instance
const cacheManager = new CacheManager();

// Hook to get user by username with enhanced caching
export const useUserByUsername = (username: string | undefined): UseUserByUsernameReturn => {
  const [data, setData] = useState<UserProfile | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchUser = useCallback(async () => {
    if (!username) {
      setData(null);
      setError(null);
      return;
    }

    const cacheKey = `user-${username}`;
    const cached = cacheManager.get<UserProfile | null>(cacheKey);

    // Return cached data if available
    if (cached) {
      setData(cached.data);
      setError(cached.error);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const userData = await getUserByUsername(username);

      // Update cache
      cacheManager.set(cacheKey, userData);

      setData(userData);
      setError(null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch user data';

      // Update cache with error
      cacheManager.set(cacheKey, null, errorMessage);

      setData(null);
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [username]);

  const refetch = useCallback(async () => {
    if (username) {
      // Clear cache for this user to force refetch
      cacheManager.delete(`user-${username}`);
      await fetchUser();
    }
  }, [username, fetchUser]);

  useEffect(() => {
    fetchUser();
  }, [fetchUser]);

  return {
    data,
    isLoading,
    error,
    refetch
  };
};

// Hook to get all users with enhanced caching - SINGLE FIRESTORE READ PER USER
export const useAllUsers = (): UseAllUsersReturn => {
  const [data, setData] = useState<UserProfile[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const fetchAllUsers = useCallback(async () => {
    const cacheKey = 'all-users';
    const cached = cacheManager.get<UserProfile[]>(cacheKey);

    // Return cached data if available - NO FIRESTORE READ
    if (cached) {
      setData(cached.data || []);
      setError(cached.error);
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      // SINGLE FIRESTORE READ - Fetch all users using optimized pagination
      let allUsers: UserProfile[] = [];
      let lastDoc: any = undefined;
      let hasMore = true;

      while (hasMore) {
        const { users, lastDoc: newLastDoc } = await getUsers(100, lastDoc);
        allUsers = allUsers.concat(users);

        if (!newLastDoc || users.length === 0) {
          hasMore = false;
        } else {
          lastDoc = newLastDoc;
        }
      }

      // Update cache with all users - CACHED FOR 1 HOUR
      cacheManager.set(cacheKey, allUsers);

      setData(allUsers);
      setError(null);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch users';

      // Update cache with error
      cacheManager.set(cacheKey, [], errorMessage);

      setData([]);
      setError(errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, []);

  const refetch = useCallback(async () => {
    // Clear cache for all users to force refetch
    cacheManager.delete('all-users');
    await fetchAllUsers();
  }, [fetchAllUsers]);

  useEffect(() => {
    fetchAllUsers();
  }, [fetchAllUsers]);

  return {
    data,
    isLoading,
    error,
    refetch
  };
};

// Hook for paginated users with ultra-fast performance
export const usePaginatedUsers = (itemsPerPage: number = 32): UsePaginatedUsersReturn => {
  const [data, setData] = useState<UserProfile[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [isLoadingMore, setIsLoadingMore] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [hasMore, setHasMore] = useState(true);
  const [currentPage, setCurrentPage] = useState(0);
  const [allUsers, setAllUsers] = useState<UserProfile[]>([]);

  const fetchInitialUsers = useCallback(async () => {
    const cacheKey = 'all-users';
    const cached = cacheManager.get<UserProfile[]>(cacheKey);

    // Use cached data if available - ULTRA FAST
    if (cached && cached.data) {
      const users = cached.data;
      setAllUsers(users);

      // Prioritize users: Featured > Verified > New > Normal
      const featuredUsers = users.filter(user => user.isFeatured);
      const verifiedUsers = users.filter(user => user.isVerified && !user.isFeatured);
      const newUsers = users.filter(user =>
        !user.isFeatured &&
        !user.isVerified &&
        isNewProfile(user.createdAt, user.updatedAt)
      );
      const normalUsers = users.filter(user =>
        !user.isFeatured &&
        !user.isVerified &&
        !isNewProfile(user.createdAt, user.updatedAt)
      );

      const prioritizedUsers = [
        ...featuredUsers,
        ...verifiedUsers,
        ...newUsers,
        ...normalUsers
      ];

      const initialUsers = prioritizedUsers.slice(0, itemsPerPage);
      setData(initialUsers);
      setHasMore(prioritizedUsers.length > itemsPerPage);
      setCurrentPage(0);
      setError(cached.error);
      return;
    }

    // If no cache, fetch from Firestore - SINGLE READ PER USER
    setIsLoading(true);
    setError(null);

    try {
      let allUsersData: UserProfile[] = [];
      let lastDoc: any = undefined;
      let hasMoreData = true;

      while (hasMoreData) {
        const { users, lastDoc: newLastDoc } = await getUsers(100, lastDoc);
        allUsersData = allUsersData.concat(users);

        if (!newLastDoc || users.length === 0) {
          hasMoreData = false;
        } else {
          lastDoc = newLastDoc;
        }
      }

      // Cache all users for future use
      cacheManager.set(cacheKey, allUsersData);
      setAllUsers(allUsersData);

      // Prioritize and paginate
      const featuredUsers = allUsersData.filter(user => user.isFeatured);
      const verifiedUsers = allUsersData.filter(user => user.isVerified && !user.isFeatured);
      const newUsers = allUsersData.filter(user =>
        !user.isFeatured &&
        !user.isVerified &&
        isNewProfile(user.createdAt, user.updatedAt)
      );
      const normalUsers = allUsersData.filter(user =>
        !user.isFeatured &&
        !user.isVerified &&
        !isNewProfile(user.createdAt, user.updatedAt)
      );

      const prioritizedUsers = [
        ...featuredUsers,
        ...verifiedUsers,
        ...newUsers,
        ...normalUsers
      ];

      const initialUsers = prioritizedUsers.slice(0, itemsPerPage);
      setData(initialUsers);
      setHasMore(prioritizedUsers.length > itemsPerPage);
      setCurrentPage(0);

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Failed to fetch users';
      setError(errorMessage);
      cacheManager.set(cacheKey, [], errorMessage);
    } finally {
      setIsLoading(false);
    }
  }, [itemsPerPage]);

  const loadMore = useCallback(async () => {
    if (!hasMore || isLoadingMore || allUsers.length === 0) return;

    setIsLoadingMore(true);

    try {
      // Prioritize users from cached data
      const featuredUsers = allUsers.filter(user => user.isFeatured);
      const verifiedUsers = allUsers.filter(user => user.isVerified && !user.isFeatured);
      const newUsers = allUsers.filter(user =>
        !user.isFeatured &&
        !user.isVerified &&
        isNewProfile(user.createdAt, user.updatedAt)
      );
      const normalUsers = allUsers.filter(user =>
        !user.isFeatured &&
        !user.isVerified &&
        !isNewProfile(user.createdAt, user.updatedAt)
      );

      const prioritizedUsers = [
        ...featuredUsers,
        ...verifiedUsers,
        ...newUsers,
        ...normalUsers
      ];

      const nextPage = currentPage + 1;
      const startIndex = nextPage * itemsPerPage;
      const endIndex = startIndex + itemsPerPage;
      const nextBatch = prioritizedUsers.slice(startIndex, endIndex);

      if (nextBatch.length > 0) {
        setData(prev => [...prev, ...nextBatch]);
        setCurrentPage(nextPage);
        setHasMore(endIndex < prioritizedUsers.length);
      } else {
        setHasMore(false);
      }

    } catch (err) {
      setError('Failed to load more users');
    } finally {
      setIsLoadingMore(false);
    }
  }, [hasMore, isLoadingMore, allUsers, currentPage, itemsPerPage]);

  const refetch = useCallback(async () => {
    cacheManager.delete('all-users');
    setData([]);
    setAllUsers([]);
    setCurrentPage(0);
    setHasMore(true);
    setError(null);
    await fetchInitialUsers();
  }, [fetchInitialUsers]);

  useEffect(() => {
    fetchInitialUsers();
  }, [fetchInitialUsers]);

  return {
    data,
    isLoading,
    isLoadingMore,
    error,
    hasMore,
    loadMore,
    refetch
  };
};

// Helper function to check if profile is new
const isNewProfile = (createdAt: number | undefined, updatedAt: number | undefined): boolean => {
  if (!createdAt) return false;

  const now = Date.now();
  const twentyFourHours = 24 * 60 * 60 * 1000;
  const oneHour = 60 * 60 * 1000;

  const isRecentlyCreated = now - createdAt < twentyFourHours;

  if (!updatedAt) {
    return isRecentlyCreated;
  }

  const timeBetweenCreateAndUpdate = updatedAt - createdAt;
  const isTrulyNew = isRecentlyCreated && timeBetweenCreateAndUpdate < oneHour;

  return isTrulyNew;
};

// Hook to invalidate cache
export const useInvalidateUserCache = (): UseInvalidateCacheReturn => {
  const mutateAsync = useCallback(async () => {
    // Clear all user-related cache entries
    cacheManager.clear();
  }, []);

  return {
    mutateAsync
  };
};

// Utility function to clear cache
export const clearUserCache = (username?: string) => {
  if (username) {
    cacheManager.delete(`user-${username}`);
  } else {
    cacheManager.clear();
  }
};

// Utility function to clear specific cache entries
export const clearCacheByPattern = (pattern: string) => {
  const stats = cacheManager.getStats();
  stats.entries.forEach(entry => {
    if (entry.key.includes(pattern)) {
      cacheManager.delete(entry.key);
    }
  });
};

// Get cache statistics for debugging
export const getCacheStats = () => {
  return cacheManager.getStats();
};

// Performance monitoring hook
export const useCachePerformance = () => {
  const [stats, setStats] = useState(getCacheStats());

  const refreshStats = useCallback(() => {
    setStats(getCacheStats());
  }, []);

  useEffect(() => {
    const interval = setInterval(refreshStats, 10000); // Update every 10 seconds
    return () => clearInterval(interval);
  }, [refreshStats]);

  return {
    stats,
    refreshStats,
    clearCache: () => {
      cacheManager.clear();
      refreshStats();
    }
  };
};

// Export cache manager for direct access if needed
export { cacheManager };