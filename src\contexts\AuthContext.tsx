import React, { createContext, useContext, useEffect, useState, type ReactNode } from 'react';
import { 
  signInWithGoogle, 
  signOut, 
  onAuthStateChange, 
  getCurrentUserProfile,
  type AuthUser 
} from '@/services/auth';
import type { UserProfile } from '@/services/firebase-db';

interface AuthContextType {
  user: AuthUser | null;
  userProfile: UserProfile | null;
  loading: boolean;
  signInWithGoogle: () => Promise<void>;
  signOut: () => Promise<void>;
  refreshUserProfile: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<AuthUser | null>(null);
  const [userProfile, setUserProfile] = useState<UserProfile | null>(null);
  const [loading, setLoading] = useState(true);

  // Refresh user profile from Firestore
  const refreshUserProfile = async () => {
    if (user) {
      try {
        const profile = await getCurrentUserProfile();
        setUserProfile(profile);
        // Update user hasProfile status
        setUser(prev => prev ? { ...prev, hasProfile: !!profile } : null);
      } catch (error) {
        console.error('Error refreshing user profile:', error);
      }
    }
  };

  // Handle Google sign in
  const handleSignInWithGoogle = async () => {
    try {
      setLoading(true);
      const result = await signInWithGoogle();
      if (result) {
        setUser(result.user);
        // Refresh profile after sign in
        await refreshUserProfile();
      }
    } catch (error) {
      console.error('Error signing in:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Handle sign out
  const handleSignOut = async () => {
    try {
      setLoading(true);
      await signOut();
      setUser(null);
      setUserProfile(null);
    } catch (error) {
      console.error('Error signing out:', error);
      throw error;
    } finally {
      setLoading(false);
    }
  };

  // Listen to auth state changes
  useEffect(() => {
    const unsubscribe = onAuthStateChange(async (authUser) => {
      setUser(authUser);

      if (authUser) {
        // Get user profile from Firestore directly here instead of using refreshUserProfile
        try {
          const profile = await getCurrentUserProfile();
          setUserProfile(profile);
          // Update the authUser hasProfile status
          setUser(prev => prev ? { ...prev, hasProfile: !!profile } : authUser);
        } catch (error) {
          console.error('Error getting user profile:', error);
          setUserProfile(null);
        }
      } else {
        setUserProfile(null);
      }

      setLoading(false);
    });

    return unsubscribe;
  }, []);

  const value: AuthContextType = {
    user,
    userProfile,
    loading,
    signInWithGoogle: handleSignInWithGoogle,
    signOut: handleSignOut,
    refreshUserProfile
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
