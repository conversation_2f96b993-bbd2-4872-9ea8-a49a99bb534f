import { defineConfig } from 'vite';
import react from '@vitejs/plugin-react';
import { resolve } from 'path';
import { visualizer } from 'rollup-plugin-visualizer';

// Bundle optimization configuration
export default defineConfig({
  plugins: [
    react(),
    // Bundle analyzer - generates stats.html
    visualizer({
      filename: 'dist/stats.html',
      open: true,
      gzipSize: true,
      brotliSize: true,
    }),
  ],
  resolve: {
    alias: {
      '@': resolve(__dirname, './src'),
    },
  },
  build: {
    // Enable tree shaking
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true, // Remove console.logs in production
        drop_debugger: true,
        pure_funcs: ['console.log', 'console.info', 'console.debug'],
      },
    },
    // Optimize chunk splitting
    rollupOptions: {
      output: {
        manualChunks: {
          // Vendor chunks
          'react-vendor': ['react', 'react-dom', 'react-router-dom'],
          'firebase-vendor': ['firebase/app', 'firebase/auth', 'firebase/firestore'],
          'ui-vendor': ['lucide-react', '@radix-ui/react-dialog', '@radix-ui/react-dropdown-menu'],
          
          // Feature chunks
          'auth-features': [
            './src/contexts/AuthContext',
            './src/services/auth',
            './src/components/ProtectedRoute'
          ],
          'cache-features': [
            './src/hooks/useFirebaseCache',
            './src/services/firebase-db'
          ],
          'ui-components': [
            './src/components/ui/button',
            './src/components/ui/card',
            './src/components/ui/skeleton',
            './src/components/ui/lazy-image'
          ]
        },
        // Optimize chunk names
        chunkFileNames: (chunkInfo) => {
          const facadeModuleId = chunkInfo.facadeModuleId
            ? chunkInfo.facadeModuleId.split('/').pop()?.replace('.tsx', '').replace('.ts', '')
            : 'chunk';
          return `js/${facadeModuleId}-[hash].js`;
        },
        assetFileNames: (assetInfo) => {
          const info = assetInfo.name?.split('.') || [];
          const ext = info[info.length - 1];
          if (/png|jpe?g|svg|gif|tiff|bmp|ico/i.test(ext || '')) {
            return `images/[name]-[hash][extname]`;
          }
          if (/css/i.test(ext || '')) {
            return `css/[name]-[hash][extname]`;
          }
          return `assets/[name]-[hash][extname]`;
        },
      },
    },
    // Increase chunk size warning limit
    chunkSizeWarningLimit: 1000,
    // Enable source maps for debugging (disable in production)
    sourcemap: process.env.NODE_ENV === 'development',
  },
  // Optimize dependencies
  optimizeDeps: {
    include: [
      'react',
      'react-dom',
      'react-router-dom',
      'firebase/app',
      'firebase/auth',
      'firebase/firestore',
      'lucide-react'
    ],
    exclude: [
      // Exclude large dependencies that should be loaded on demand
      '@dotlottie/react-player'
    ]
  },
  // Performance optimizations
  server: {
    // Enable HTTP/2 in development
    https: false,
    // Optimize HMR
    hmr: {
      overlay: false
    }
  },
  // CSS optimizations
  css: {
    devSourcemap: process.env.NODE_ENV === 'development',
    preprocessorOptions: {
      // Add any CSS preprocessing options here
    }
  },
  // Define environment variables for optimization
  define: {
    __DEV__: process.env.NODE_ENV === 'development',
    __PROD__: process.env.NODE_ENV === 'production',
  }
});

// Additional optimization tips:
/*
1. Run bundle analyzer:
   npm run build && npx vite-bundle-analyzer

2. Check for duplicate dependencies:
   npm ls --depth=0

3. Use dynamic imports for heavy components:
   const HeavyComponent = lazy(() => import('./HeavyComponent'));

4. Optimize images:
   - Use WebP format
   - Implement responsive images
   - Compress images before bundling

5. Enable compression on your server:
   - Gzip compression
   - Brotli compression (better than gzip)

6. Use a CDN for static assets:
   - Images, fonts, icons
   - Third-party libraries

7. Monitor bundle size:
   - Set up CI/CD checks for bundle size
   - Use tools like bundlephobia.com

8. Tree shaking optimization:
   - Import only what you need: import { Button } from './ui/button'
   - Avoid default imports from large libraries
   - Use ES modules instead of CommonJS

9. Code splitting strategies:
   - Route-based splitting (already implemented)
   - Feature-based splitting
   - Vendor splitting (already implemented)

10. Performance monitoring:
    - Use the performance hooks we created
    - Monitor Core Web Vitals
    - Set up real user monitoring (RUM)
*/
