export const languageColors: { [key: string]: string } = {
  "TypeScript": "#3178c6",
  "JavaScript": "#f1e05a",
  "Python": "#3572A5",
  "Java": "#b07219",
  "C++": "#f34b7d",
  "C#": "#178600",
  "PHP": "#4F5D95",
  "HTML": "#e34c26",
  "CSS": "#563d7c",
  "Ruby": "#701516",
  "Go": "#00ADD8",
  "Swift": "#F05138",
  "Kotlin": "#A97BFF",
  "Rust": "#dea584",
  "Shell": "#89e051",
  "PowerShell": "#012456",
  "Objective-C": "#438eff",
  "Scala": "#c22d40",
  "Perl": "#0298c3",
  "Lua": "#000080",
  "Dart": "#00B4AB",
  "Vue": "#41b883",
  "Svelte": "#ff3e00",
  "Makefile": "#427819",
  "Dockerfile": "#384d54",
  "Haskell": "#5e5086",
  "R": "#198CE7",
  "Jupyter Notebook": "#DA5B0B",
  "C": "#555555",
  "Default": "#ccc"
};

export const getLanguageColor = (language: string): string => {
  return languageColors[language] || languageColors.Default;
}; 