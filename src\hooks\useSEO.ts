import { useEffect, useMemo } from 'react';
import { useLocation } from 'react-router-dom';
import { generatePageSEO, generateCanonicalUrl } from '../utils/seo';

interface UseSEOProps {
  page?: string;
  data?: any;
  customSEO?: {
    title?: string;
    description?: string;
    keywords?: string[];
    image?: string;
    noIndex?: boolean;
  };
}

export const useSEO = ({ page, data, customSEO }: UseSEOProps = {}) => {
  const location = useLocation();
  
  // Determine page type from location if not provided
  const currentPage = useMemo(() => {
    if (page) return page;
    
    const pathname = location.pathname;
    if (pathname === '/') return 'home';
    if (pathname === '/portfolios') return 'portfolios';
    if (pathname === '/create-profile') return 'create-profile';
    if (pathname === '/pricing') return 'pricing';
    if (pathname.startsWith('/') && pathname.length > 1) return 'profile';
    
    return 'default';
  }, [page, location.pathname]);

  // Generate SEO data
  const seoData = useMemo(() => {
    const baseSEO = generatePageSEO(currentPage, data);
    
    // Merge with custom SEO if provided
    if (customSEO) {
      return {
        ...baseSEO,
        ...customSEO,
        keywords: customSEO.keywords || baseSEO.keywords,
      };
    }
    
    return baseSEO;
  }, [currentPage, data, customSEO]);

  // Generate canonical URL
  const canonicalUrl = useMemo(() => {
    return generateCanonicalUrl(location.pathname);
  }, [location.pathname]);

  // Track page views for analytics (optional)
  useEffect(() => {
    // Google Analytics page view tracking
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('config', 'GA_MEASUREMENT_ID', {
        page_title: seoData.title,
        page_location: canonicalUrl,
      });
    }

    // Facebook Pixel page view tracking
    if (typeof window !== 'undefined' && (window as any).fbq) {
      (window as any).fbq('track', 'PageView');
    }
  }, [seoData.title, canonicalUrl]);

  return {
    ...seoData,
    canonicalUrl,
    currentPage,
  };
};

// Hook for tracking specific events
export const useSEOEvents = () => {
  const trackEvent = (eventName: string, parameters?: Record<string, any>) => {
    // Google Analytics event tracking
    if (typeof window !== 'undefined' && (window as any).gtag) {
      (window as any).gtag('event', eventName, parameters);
    }

    // Facebook Pixel event tracking
    if (typeof window !== 'undefined' && (window as any).fbq) {
      (window as any).fbq('track', eventName, parameters);
    }

    // Custom analytics tracking
    console.log('SEO Event:', eventName, parameters);
  };

  const trackProfileView = (username: string, userRole?: string) => {
    trackEvent('profile_view', {
      username,
      user_role: userRole,
      event_category: 'engagement',
    });
  };

  const trackSearch = (searchTerm: string, resultsCount: number) => {
    trackEvent('search', {
      search_term: searchTerm,
      results_count: resultsCount,
      event_category: 'search',
    });
  };

  const trackContactClick = (contactType: 'github' | 'linkedin' | 'email', username: string) => {
    trackEvent('contact_click', {
      contact_type: contactType,
      username,
      event_category: 'engagement',
    });
  };

  const trackFeatureClick = (featureName: string) => {
    trackEvent('feature_click', {
      feature_name: featureName,
      event_category: 'feature_usage',
    });
  };

  const trackSignup = (method: 'google' | 'email') => {
    trackEvent('sign_up', {
      method,
      event_category: 'conversion',
    });
  };

  const trackProfileCreation = (userRole?: string, techStack?: string[]) => {
    trackEvent('profile_created', {
      user_role: userRole,
      tech_stack: techStack?.join(','),
      event_category: 'conversion',
    });
  };

  return {
    trackEvent,
    trackProfileView,
    trackSearch,
    trackContactClick,
    trackFeatureClick,
    trackSignup,
    trackProfileCreation,
  };
};

// Hook for performance monitoring related to SEO
export const useSEOPerformance = () => {
  useEffect(() => {
    // Monitor Core Web Vitals for SEO
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
      // Largest Contentful Paint (LCP)
      const lcpObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        const lastEntry = entries[entries.length - 1] as any;
        if (lastEntry && (window as any).gtag) {
          (window as any).gtag('event', 'LCP', {
            event_category: 'Web Vitals',
            value: Math.round(lastEntry.startTime),
            non_interaction: true,
          });
        }
      });

      // First Input Delay (FID)
      const fidObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          if ((window as any).gtag) {
            (window as any).gtag('event', 'FID', {
              event_category: 'Web Vitals',
              value: Math.round(entry.processingStart - entry.startTime),
              non_interaction: true,
            });
          }
        });
      });

      // Cumulative Layout Shift (CLS)
      let clsValue = 0;
      const clsObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach((entry: any) => {
          if (!entry.hadRecentInput) {
            clsValue += entry.value;
          }
        });
        
        if ((window as any).gtag) {
          (window as any).gtag('event', 'CLS', {
            event_category: 'Web Vitals',
            value: Math.round(clsValue * 1000),
            non_interaction: true,
          });
        }
      });

      try {
        lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });
        fidObserver.observe({ entryTypes: ['first-input'] });
        clsObserver.observe({ entryTypes: ['layout-shift'] });
      } catch (error) {
        console.warn('Performance observers not supported:', error);
      }

      return () => {
        lcpObserver.disconnect();
        fidObserver.disconnect();
        clsObserver.disconnect();
      };
    }
  }, []);
};

// Hook for structured data management
export const useStructuredData = (data: object) => {
  useEffect(() => {
    if (!data) return;

    // Create or update structured data script
    const scriptId = 'structured-data';
    let script = document.getElementById(scriptId) as HTMLScriptElement;
    
    if (!script) {
      script = document.createElement('script');
      script.id = scriptId;
      script.type = 'application/ld+json';
      document.head.appendChild(script);
    }
    
    script.textContent = JSON.stringify(data);

    return () => {
      // Cleanup on unmount
      const existingScript = document.getElementById(scriptId);
      if (existingScript) {
        existingScript.remove();
      }
    };
  }, [data]);
};
