import { createBrowserRouter, Navigate } from "react-router-dom";
import App from "../App";
import Portfolios from "../pages/Portfolios";
import CreateProfile from "../pages/create-profile";
import UpdateProfile from "../pages/update-profile";
import Profile from "../pages/Profile";
import Settings from "../pages/Settings";
import ProtectedRoute from "../components/ProtectedRoute";
import Home from "../pages/Home";
import AdminPanel from "../pages/admin-panel";
import Payment from "@/pages/payment";
import PricingPage from "@/pages/pricing-page";
import NotFound from "@/pages/notFound";

const router = createBrowserRouter([
  {
    path: "/",
    element: <App />,
    children : [
        {
            path: "/",
            element: <Home />
        },
        {
            path: "/portfolios",
            element: <Portfolios />
        },
        {
            path: "/create-profile",
            element: (
              <ProtectedRoute requireAuth={true}>
                <CreateProfile />
              </ProtectedRoute>
            )
        },
        {
            path: "/update-profile",
            element: (
              <ProtectedRoute requireAuth={true} requireProfile={true}>
                <UpdateProfile />
              </ProtectedRoute>
            )
        },
        {
            path: "/profile",
            element: <Navigate to="/" replace />
        },
        {
            path: "/:username",
            element: <Profile />
        },
        {
            path: "/settings",
            element: (
              <ProtectedRoute requireAuth={true} requireProfile={true}>
                <Settings />
              </ProtectedRoute>
            )
        },
        {
            path: "/admin-panel",
            element: (
              <ProtectedRoute requireAdmin={true}>
                <AdminPanel />
              </ProtectedRoute>
            )
        },
        {
            path: "/payment",
            element: (
              <ProtectedRoute requireAuth={true}>
                <Payment />
              </ProtectedRoute>
            )
        },
        {
            path: "/pricing",
            element: (
              <ProtectedRoute requireAuth={true}>
                <PricingPage />
              </ProtectedRoute>
            )
        },
        {
            path: "*",
            element: <NotFound />
        }
    ]
  }
]);

export default router;
