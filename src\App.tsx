import { Outlet } from "react-router-dom"
import Header from "./pages/Header"
import { usePerformanceMonitor } from "./hooks/usePerformanceMonitor"
import { PerformanceOptimizer } from "./components/ui/performance-optimizer"

export default function App() {
  // Initialize performance monitoring
  usePerformanceMonitor({
    enableLogging: process.env.NODE_ENV === 'development',
    enableReporting: false, // Set to true if you have a reporting endpoint
    sampleRate: 0.1 // Only report 10% of sessions to avoid spam
  });

  return (
    <PerformanceOptimizer>
      <Header />
      <div className="h-full pt-16">
        <Outlet />
      </div>
    </PerformanceOptimizer>
  )
}
