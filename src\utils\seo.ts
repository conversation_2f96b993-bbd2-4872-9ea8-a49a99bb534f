// SEO utility functions and constants

export const SEO_CONSTANTS = {
  SITE_NAME: 'DevPortfolio',
  SITE_URL: typeof window !== 'undefined' ? window.location.origin : 'https://devportfolio.com',
  DEFAULT_IMAGE: '/og-image.jpg',
  TWITTER_HANDLE: '@devportfolio',
  FACEBOOK_APP_ID: '',
  DEFAULT_LOCALE: 'en_US',
  ORGANIZATION_NAME: 'DevPortfolio',
  ORGANIZATION_LOGO: '/logo.png',
};

export const DEFAULT_KEYWORDS = [
  'developer portfolio',
  'programming',
  'software development',
  'web development',
  'mobile development',
  'frontend developer',
  'backend developer',
  'full stack developer',
  'react developer',
  'javascript developer',
  'python developer',
  'portfolio showcase',
  'developer community',
  'coding projects',
  'tech talent',
  'hire developers',
  'developer jobs',
  'programming skills',
  'software engineer',
  'developer network',
  'coding bootcamp',
  'tech portfolio',
  'developer showcase',
  'programming portfolio',
  'software portfolio'
];

// Generate page-specific SEO data
export const generatePageSEO = (page: string, data?: any) => {
  const baseUrl = SEO_CONSTANTS.SITE_URL;
  
  switch (page) {
    case 'home':
      return {
        title: 'DevPortfolio - Discover & Showcase Developer Talent',
        description: 'Connect with talented developers worldwide. Showcase your programming skills, explore amazing portfolios, and find your next team member or career opportunity.',
        keywords: [...DEFAULT_KEYWORDS, 'developer community', 'tech networking', 'programming showcase'],
        url: baseUrl,
        image: `${baseUrl}/og-home.jpg`,
        structuredData: {
          "@context": "https://schema.org",
          "@type": "WebSite",
          "name": SEO_CONSTANTS.SITE_NAME,
          "description": "Connect with talented developers worldwide. Showcase your programming skills and explore amazing portfolios.",
          "url": baseUrl,
          "potentialAction": {
            "@type": "SearchAction",
            "target": {
              "@type": "EntryPoint",
              "urlTemplate": `${baseUrl}/portfolios?search={search_term_string}`
            },
            "query-input": "required name=search_term_string"
          }
        }
      };

    case 'portfolios':
      return {
        title: 'Developer Portfolios - Browse Talented Programmers | DevPortfolio',
        description: 'Browse through hundreds of developer portfolios. Find frontend, backend, full-stack, mobile, and specialized developers for your next project.',
        keywords: [...DEFAULT_KEYWORDS, 'browse developers', 'hire programmers', 'developer directory'],
        url: `${baseUrl}/portfolios`,
        image: `${baseUrl}/og-portfolios.jpg`,
        structuredData: {
          "@context": "https://schema.org",
          "@type": "CollectionPage",
          "name": "Developer Portfolios",
          "description": "Browse through hundreds of developer portfolios from around the world.",
          "url": `${baseUrl}/portfolios`,
          "mainEntity": {
            "@type": "ItemList",
            "name": "Developer Portfolios",
            "description": "A curated list of developer portfolios"
          }
        }
      };

    case 'profile':
      const user = data?.user;
      if (!user) return generatePageSEO('portfolios');
      
      const userKeywords = [
        ...(user.techStack || []),
        user.role || 'developer',
        user.seniorityLevel || '',
        user.country || '',
        'developer portfolio',
        'hire developer',
        'programming skills'
      ].filter(Boolean);

      return {
        title: `${user.displayName || user.username} - ${user.role || 'Developer'} Portfolio | DevPortfolio`,
        description: `${user.bio || `Talented ${user.role || 'developer'} specializing in ${(user.techStack || []).slice(0, 3).join(', ')}.`} Available for work: ${user.availableForWork ? 'Yes' : 'No'}.`,
        keywords: [...DEFAULT_KEYWORDS, ...userKeywords],
        url: `${baseUrl}/${user.username}`,
        image: user.bannerURL || user.photoURL || `${baseUrl}/og-profile.jpg`,
        type: 'profile' as const,
        author: user.displayName || user.username,
        structuredData: {
          "@context": "https://schema.org",
          "@type": "Person",
          "name": user.displayName || user.username,
          "description": user.bio || `${user.role || 'Developer'} specializing in ${(user.techStack || []).slice(0, 3).join(', ')}`,
          "image": user.photoURL || `${baseUrl}/default-avatar.jpg`,
          "url": `${baseUrl}/${user.username}`,
          "jobTitle": user.role || 'Developer',
          "worksFor": {
            "@type": "Organization",
            "name": "Freelance"
          },
          "knowsAbout": user.techStack || [],
          "sameAs": [
            user.githubUrl,
            user.linkedinUrl
          ].filter(Boolean)
        }
      };

    case 'create-profile':
      return {
        title: 'Create Your Developer Portfolio | DevPortfolio',
        description: 'Create a stunning developer portfolio in minutes. Showcase your programming skills, projects, and experience to potential employers and clients.',
        keywords: [...DEFAULT_KEYWORDS, 'create portfolio', 'developer profile', 'portfolio builder'],
        url: `${baseUrl}/create-profile`,
        image: `${baseUrl}/og-create.jpg`,
        noIndex: true // Don't index form pages
      };

    case 'pricing':
      return {
        title: 'Pricing Plans - Premium Developer Features | DevPortfolio',
        description: 'Choose the perfect plan for your developer portfolio. Get featured placement, advanced analytics, and premium customization options.',
        keywords: [...DEFAULT_KEYWORDS, 'pricing', 'premium features', 'developer plans'],
        url: `${baseUrl}/pricing`,
        image: `${baseUrl}/og-pricing.jpg`,
        structuredData: {
          "@context": "https://schema.org",
          "@type": "Product",
          "name": "DevPortfolio Premium",
          "description": "Premium features for developer portfolios",
          "offers": {
            "@type": "Offer",
            "priceCurrency": "USD",
            "price": "9.99",
            "priceValidUntil": "2025-12-31"
          }
        }
      };

    default:
      return {
        title: 'DevPortfolio - Showcase Your Development Skills',
        description: 'Discover talented developers and showcase your programming skills. Connect with developers worldwide and explore amazing portfolios.',
        keywords: DEFAULT_KEYWORDS,
        url: baseUrl,
        image: `${baseUrl}/og-default.jpg`
      };
  }
};

// Generate breadcrumb structured data
export const generateBreadcrumbData = (breadcrumbs: { name: string; url: string }[]) => {
  return {
    "@context": "https://schema.org",
    "@type": "BreadcrumbList",
    "itemListElement": breadcrumbs.map((crumb, index) => ({
      "@type": "ListItem",
      "position": index + 1,
      "name": crumb.name,
      "item": crumb.url
    }))
  };
};

// Generate organization structured data
export const generateOrganizationData = () => {
  return {
    "@context": "https://schema.org",
    "@type": "Organization",
    "name": SEO_CONSTANTS.ORGANIZATION_NAME,
    "url": SEO_CONSTANTS.SITE_URL,
    "logo": `${SEO_CONSTANTS.SITE_URL}${SEO_CONSTANTS.ORGANIZATION_LOGO}`,
    "description": "A platform for developers to showcase their skills and connect with opportunities",
    "sameAs": [
      "https://github.com/devportfolio",
      "https://twitter.com/devportfolio",
      "https://linkedin.com/company/devportfolio"
    ]
  };
};

// Generate FAQ structured data
export const generateFAQData = (faqs: { question: string; answer: string }[]) => {
  return {
    "@context": "https://schema.org",
    "@type": "FAQPage",
    "mainEntity": faqs.map(faq => ({
      "@type": "Question",
      "name": faq.question,
      "acceptedAnswer": {
        "@type": "Answer",
        "text": faq.answer
      }
    }))
  };
};

// Clean and optimize meta description
export const optimizeMetaDescription = (text: string, maxLength: number = 160): string => {
  if (!text) return '';
  
  // Remove HTML tags
  const cleanText = text.replace(/<[^>]*>/g, '');
  
  // Trim and limit length
  if (cleanText.length <= maxLength) return cleanText;
  
  // Cut at word boundary
  const trimmed = cleanText.substring(0, maxLength);
  const lastSpace = trimmed.lastIndexOf(' ');
  
  return lastSpace > 0 ? trimmed.substring(0, lastSpace) + '...' : trimmed + '...';
};

// Generate canonical URL
export const generateCanonicalUrl = (path: string): string => {
  const baseUrl = SEO_CONSTANTS.SITE_URL;
  const cleanPath = path.startsWith('/') ? path : `/${path}`;
  return `${baseUrl}${cleanPath}`;
};

// Generate sitemap data
export const generateSitemapUrls = (users: any[] = []) => {
  const baseUrl = SEO_CONSTANTS.SITE_URL;
  const staticPages = [
    { url: baseUrl, priority: 1.0, changefreq: 'daily' },
    { url: `${baseUrl}/portfolios`, priority: 0.9, changefreq: 'daily' },
    { url: `${baseUrl}/pricing`, priority: 0.7, changefreq: 'weekly' },
    { url: `${baseUrl}/create-profile`, priority: 0.6, changefreq: 'monthly' }
  ];

  const userPages = users.map(user => ({
    url: `${baseUrl}/${user.username}`,
    priority: user.isFeatured ? 0.8 : 0.6,
    changefreq: 'weekly',
    lastmod: user.updatedAt ? new Date(user.updatedAt).toISOString() : undefined
  }));

  return [...staticPages, ...userPages];
};
